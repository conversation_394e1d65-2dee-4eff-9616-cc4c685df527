00:16:02.782 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:16:02.787 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:16:09.981 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 959780 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:16:09.984 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:16:11.418 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:16:11.419 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:16:11.419 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:16:11.469 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:16:12.033 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:16:12.267 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:16:12.881 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:16:12.897 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.629 seconds (JVM running for 4.338)
00:16:23.656 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:16:23.745 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
00:16:23.746 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
00:16:23.747 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
00:16:23.747 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
00:16:23.747 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
00:16:24.060 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
00:16:24.097 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 5, 总列数: 8
00:16:24.137 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1678] - 应用表头宽度配置: 列宽=[180, 150, 320, 100, 100, 30, 90, 90], 行高=[60, 40]
00:16:24.139 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1730] - 开始应用列宽配置: [180, 150, 320, 100, 100, 30, 90, 90]
00:16:24.149 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1744] - 列宽配置应用完成
00:16:24.191 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,680] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
00:16:24.193 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
00:16:24.475 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,977] - 应用表头合并单元格，数量: 5
00:16:24.476 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1073] - 开始应用 5 个合并单元格，表头行数: 0
00:16:24.476 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 产品名称
00:16:24.478 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 规格型号
00:16:24.479 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 质量检验
00:16:24.479 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 生产信息
00:16:24.481 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,5) -> (0,7) 调整后(0,5) -> (0,7) 内容: 责任人员
00:16:24.481 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,985] - 应用数据行合并单元格，数量: 1，表头偏移: 2
00:16:24.481 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1073] - 开始应用 1 个合并单元格，表头行数: 2
00:16:24.481 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(2,0) -> (3,0) 内容: 智能手机
（多功能检测）
00:16:24.534 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3487 bytes
00:16:24.546 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_001624.docx, 大小: 3487 bytes
00:20:21.202 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
00:20:21.203 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
00:20:21.203 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
00:20:21.203 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
00:20:21.203 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
00:20:21.206 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
00:20:21.207 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 3, 总列数: 8
00:20:21.211 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1678] - 应用表头宽度配置: 列宽=[180, 150, 120, 100, 100, 90, 90, 90], 行高=[60, 40]
00:20:21.211 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1730] - 开始应用列宽配置: [180, 150, 120, 100, 100, 90, 90, 90]
00:20:21.213 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1744] - 列宽配置应用完成
00:20:21.221 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,977] - 应用表头合并单元格，数量: 5
00:20:21.222 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1073] - 开始应用 5 个合并单元格，表头行数: 0
00:20:21.222 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 产品名称
00:20:21.222 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 规格型号
00:20:21.223 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 质量检验
00:20:21.223 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 生产信息
00:20:21.223 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1111] - 应用合并单元格: 原始(0,5) -> (0,7) 调整后(0,5) -> (0,7) 内容: 责任人员
00:20:21.224 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,985] - 应用数据行合并单元格，数量: 1，表头偏移: 2
00:20:21.224 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1073] - 开始应用 1 个合并单元格，表头行数: 2
00:20:21.235 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3074 bytes
00:20:21.241 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_002021.docx, 大小: 3074 bytes
00:28:31.703 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:28:31.708 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:28:41.454 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 974697 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:28:41.458 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:28:44.174 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:28:44.175 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:28:44.176 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:28:44.261 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:28:45.331 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:28:45.656 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:28:46.739 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:28:46.780 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 6.035 seconds (JVM running for 6.628)
00:28:50.445 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:28:50.550 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
00:28:50.550 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
00:28:50.551 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
00:28:50.552 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
00:28:50.552 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
00:28:50.862 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
00:28:50.920 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 3, 总列数: 8
00:28:50.967 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1687] - 应用表头宽度配置: 列宽=[180, 150, 120, 100, 100, 90, 90, 90], 行高=[60, 40]
00:28:50.970 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1739] - 开始应用列宽配置: [180, 150, 120, 100, 100, 90, 90, 90]
00:28:50.971 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1768] - === 处理第0行宽度设置 ===
00:28:50.971 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1769] - 物理单元格数: 8, 逻辑列数: 8
00:28:50.971 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:28:50.977 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行0 列0 内容='[空]' 逻辑列0 跨度1 宽度: 180像素
00:28:50.977 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:28:50.977 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行0 列1 内容='[空]' 逻辑列1 跨度1 宽度: 150像素
00:28:50.978 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:28:50.978 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行0 列2 内容='[空]' 逻辑列2 跨度1 宽度: 120像素
00:28:50.978 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:28:50.978 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行0 列3 内容='[空]' 逻辑列3 跨度1 宽度: 100像素
00:28:50.978 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:28:50.979 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行0 列4 内容='[空]' 逻辑列4 跨度1 宽度: 100像素
00:28:50.979 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:28:50.979 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行0 列5 内容='[空]' 逻辑列5 跨度1 宽度: 90像素
00:28:50.979 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:28:50.980 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行0 列6 内容='[空]' 逻辑列6 跨度1 宽度: 90像素
00:28:50.980 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:28:50.981 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行0 列7 内容='[空]' 逻辑列7 跨度1 宽度: 90像素
00:28:50.981 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1768] - === 处理第1行宽度设置 ===
00:28:50.981 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1769] - 物理单元格数: 8, 逻辑列数: 8
00:28:50.981 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:28:50.981 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行1 列0 内容='[空]' 逻辑列0 跨度1 宽度: 180像素
00:28:50.982 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:28:50.982 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行1 列1 内容='[空]' 逻辑列1 跨度1 宽度: 150像素
00:28:50.982 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:28:50.982 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行1 列2 内容='[空]' 逻辑列2 跨度1 宽度: 120像素
00:28:50.982 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:28:50.983 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行1 列3 内容='[空]' 逻辑列3 跨度1 宽度: 100像素
00:28:50.983 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:28:50.983 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行1 列4 内容='[空]' 逻辑列4 跨度1 宽度: 100像素
00:28:50.984 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:28:50.984 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行1 列5 内容='[空]' 逻辑列5 跨度1 宽度: 90像素
00:28:50.984 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:28:50.985 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行1 列6 内容='[空]' 逻辑列6 跨度1 宽度: 90像素
00:28:50.985 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:28:50.985 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行1 列7 内容='[空]' 逻辑列7 跨度1 宽度: 90像素
00:28:50.985 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1768] - === 处理第2行宽度设置 ===
00:28:50.986 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1769] - 物理单元格数: 8, 逻辑列数: 8
00:28:50.986 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:28:50.986 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行2 列0 内容='[空]' 逻辑列0 跨度1 宽度: 180像素
00:28:50.986 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:28:50.986 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行2 列1 内容='[空]' 逻辑列1 跨度1 宽度: 150像素
00:28:50.986 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:28:50.987 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行2 列2 内容='[空]' 逻辑列2 跨度1 宽度: 120像素
00:28:50.987 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:28:50.987 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行2 列3 内容='[空]' 逻辑列3 跨度1 宽度: 100像素
00:28:50.987 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:28:50.987 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行2 列4 内容='[空]' 逻辑列4 跨度1 宽度: 100像素
00:28:50.988 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:28:50.988 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行2 列5 内容='[空]' 逻辑列5 跨度1 宽度: 90像素
00:28:50.988 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:28:50.988 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行2 列6 内容='[空]' 逻辑列6 跨度1 宽度: 90像素
00:28:50.988 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1874] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:28:50.989 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 行2 列7 内容='[空]' 逻辑列7 跨度1 宽度: 90像素
00:28:50.989 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1753] - 列宽配置应用完成
00:28:51.032 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,977] - 应用表头合并单元格，数量: 5
00:28:51.032 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 5 个合并单元格，表头行数: 0
00:28:51.032 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 产品名称
00:28:51.035 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 规格型号
00:28:51.035 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 质量检验
00:28:51.035 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 生产信息
00:28:51.038 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='生产信息' 起始列3 结束列4 跨度2 实际行0
00:28:51.039 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列4 内容=''
00:28:51.039 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,5) -> (0,7) 调整后(0,5) -> (0,7) 内容: 责任人员
00:28:51.039 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='责任人员' 起始列5 结束列7 跨度3 实际行0
00:28:51.039 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列6 内容=''
00:28:51.040 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列7 内容=''
00:28:51.040 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,985] - 应用数据行合并单元格，数量: 1，表头偏移: 2
00:28:51.040 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 1 个合并单元格，表头行数: 2
00:28:51.040 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,1976] - === 表格结构信息 ===
00:28:51.040 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,1983] - 第0行，单元格数: 8
00:28:51.048 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列0: 内容='产品名称' 跨度=1 宽度=0像素(0twips)
00:28:51.048 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列1: 内容='规格型号' 跨度=1 宽度=0像素(0twips)
00:28:51.049 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列2: 内容='质量检验' 跨度=1 宽度=0像素(0twips)
00:28:51.049 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列3: 内容='生产信息' 跨度=2 宽度=0像素(0twips)
00:28:51.050 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列4: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.050 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列5: 内容='责任人员' 跨度=3 宽度=0像素(0twips)
00:28:51.050 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列6: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.050 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列7: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.050 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2019] - 
00:28:51.050 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,1983] - 第1行，单元格数: 8
00:28:51.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列0: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列1: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列2: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列3: 内容='批次号' 跨度=1 宽度=0像素(0twips)
00:28:51.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列4: 内容='日期' 跨度=1 宽度=0像素(0twips)
00:28:51.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列5: 内容='检验员' 跨度=1 宽度=0像素(0twips)
00:28:51.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列6: 内容='审核员' 跨度=1 宽度=0像素(0twips)
00:28:51.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列7: 内容='负责人' 跨度=1 宽度=0像素(0twips)
00:28:51.053 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2019] - 
00:28:51.053 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,1983] - 第2行，单元格数: 8
00:28:51.053 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列0: 内容='智能手机\n（多功能检测）' 跨度=1 宽度=0像素(0twips)
00:28:51.054 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列1: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.054 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列2: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.054 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列3: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.054 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列4: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.054 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列5: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.054 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列6: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.055 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2015] -   列7: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:28:51.055 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2019] - 
00:28:51.055 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2022] - === 表格结构信息结束 ===
00:28:51.163 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3074 bytes
00:28:51.181 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_002851.docx, 大小: 3074 bytes
00:36:38.339 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:36:38.346 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:36:59.683 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 984654 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:36:59.686 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:37:00.920 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:37:00.921 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:37:00.921 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:37:00.983 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:37:01.596 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:37:01.869 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:37:02.658 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:37:02.676 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.624 seconds (JVM running for 4.365)
00:37:06.569 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:37:06.759 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
00:37:06.760 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
00:37:06.761 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
00:37:06.761 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
00:37:06.761 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
00:37:07.130 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
00:37:07.182 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 3, 总列数: 8
00:37:07.234 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1708] - 应用表头宽度配置: 列宽=[180, 150, 120, 100, 100, 90, 90, 90], 行高=[60, 40]
00:37:07.237 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1718] - 准备应用列宽配置，配置内容: [180, 150, 120, 100, 100, 90, 90, 90]
00:37:07.238 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1764] - 开始应用列宽配置: [180, 150, 120, 100, 100, 90, 90, 90]
00:37:07.238 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第0行宽度设置 ===
00:37:07.238 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
00:37:07.239 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:37:07.245 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列0 内容='[空]' 逻辑列0 跨度1 设置宽度: 180像素
00:37:07.245 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:37:07.246 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
00:37:07.246 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:37:07.246 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
00:37:07.247 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:37:07.247 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
00:37:07.248 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:37:07.248 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
00:37:07.249 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:37:07.249 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:37:07.249 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:37:07.250 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
00:37:07.250 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:37:07.251 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
00:37:07.251 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第1行宽度设置 ===
00:37:07.251 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
00:37:07.251 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:37:07.251 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列0 内容='[空]' 逻辑列0 跨度1 设置宽度: 180像素
00:37:07.252 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:37:07.252 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
00:37:07.252 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:37:07.252 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
00:37:07.253 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:37:07.253 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
00:37:07.253 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:37:07.253 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
00:37:07.253 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:37:07.254 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:37:07.255 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:37:07.255 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
00:37:07.255 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:37:07.256 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
00:37:07.256 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第2行宽度设置 ===
00:37:07.256 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
00:37:07.257 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:37:07.257 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列0 内容='[空]' 逻辑列0 跨度1 设置宽度: 180像素
00:37:07.257 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:37:07.257 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
00:37:07.257 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:37:07.258 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
00:37:07.258 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:37:07.258 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
00:37:07.258 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:37:07.258 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
00:37:07.259 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:37:07.259 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:37:07.259 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:37:07.259 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
00:37:07.259 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:37:07.260 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
00:37:07.260 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1778] - 列宽配置应用完成
00:37:07.305 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,977] - 应用表头合并单元格，数量: 5
00:37:07.305 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 5 个合并单元格，表头行数: 0
00:37:07.305 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 产品名称
00:37:07.308 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 规格型号
00:37:07.309 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 质量检验
00:37:07.309 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 生产信息
00:37:07.311 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='生产信息' 起始列3 结束列4 跨度2 实际行0
00:37:07.312 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列4 内容=''
00:37:07.312 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,5) -> (0,7) 调整后(0,5) -> (0,7) 内容: 责任人员
00:37:07.313 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='责任人员' 起始列5 结束列7 跨度3 实际行0
00:37:07.313 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列6 内容=''
00:37:07.313 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列7 内容=''
00:37:07.313 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,985] - 应用数据行合并单元格，数量: 1，表头偏移: 2
00:37:07.313 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 1 个合并单元格，表头行数: 2
00:37:07.314 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2019] - === 表格结构信息 ===
00:37:07.314 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第0行，单元格数: 8
00:37:07.321 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='产品名称' 跨度=1 宽度=180像素(2700twips)
00:37:07.322 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='规格型号' 跨度=1 宽度=150像素(2250twips)
00:37:07.323 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='质量检验' 跨度=1 宽度=120像素(1800twips)
00:37:07.324 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='生产信息' 跨度=2 宽度=100像素(1500twips)
00:37:07.324 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:37:07.324 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='责任人员' 跨度=3 宽度=90像素(1350twips)
00:37:07.325 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:37:07.325 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:37:07.325 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:37:07.325 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第1行，单元格数: 8
00:37:07.325 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='[空]' 跨度=1 宽度=180像素(2700twips)
00:37:07.325 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
00:37:07.326 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
00:37:07.326 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='批次号' 跨度=1 宽度=100像素(1500twips)
00:37:07.326 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='日期' 跨度=1 宽度=100像素(1500twips)
00:37:07.326 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='检验员' 跨度=1 宽度=90像素(1350twips)
00:37:07.326 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='审核员' 跨度=1 宽度=90像素(1350twips)
00:37:07.327 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='负责人' 跨度=1 宽度=90像素(1350twips)
00:37:07.327 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:37:07.327 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第2行，单元格数: 8
00:37:07.327 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='智能手机\n（多功能检测）' 跨度=1 宽度=180像素(2700twips)
00:37:07.327 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
00:37:07.327 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
00:37:07.328 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:37:07.328 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:37:07.328 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:37:07.328 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:37:07.328 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:37:07.328 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:37:07.328 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2073] - === 表格结构信息结束 ===
00:37:07.436 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3074 bytes
00:37:07.450 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_003707.docx, 大小: 3074 bytes
00:40:53.509 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
00:40:53.510 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
00:40:53.510 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
00:40:53.510 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
00:40:53.510 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
00:40:53.513 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
00:40:53.514 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 3, 总列数: 8
00:40:53.516 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1708] - 应用表头宽度配置: 列宽=[180, 150, 120, 100, 100, 90, 90, 90], 行高=[60, 40]
00:40:53.516 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1718] - 准备应用列宽配置，配置内容: [180, 150, 120, 100, 100, 90, 90, 90]
00:40:53.516 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1764] - 开始应用列宽配置: [180, 150, 120, 100, 100, 90, 90, 90]
00:40:53.516 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第0行宽度设置 ===
00:40:53.516 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
00:40:53.516 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:40:53.517 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列0 内容='[空]' 逻辑列0 跨度1 设置宽度: 180像素
00:40:53.517 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:40:53.517 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
00:40:53.517 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:40:53.518 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
00:40:53.518 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:40:53.518 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
00:40:53.518 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:40:53.518 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
00:40:53.518 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:40:53.519 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:40:53.519 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:40:53.519 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
00:40:53.519 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:40:53.519 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
00:40:53.519 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第1行宽度设置 ===
00:40:53.519 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
00:40:53.519 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:40:53.520 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列0 内容='[空]' 逻辑列0 跨度1 设置宽度: 180像素
00:40:53.520 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:40:53.520 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
00:40:53.520 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:40:53.520 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
00:40:53.520 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:40:53.521 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
00:40:53.521 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:40:53.521 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
00:40:53.521 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:40:53.521 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:40:53.522 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:40:53.522 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
00:40:53.522 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:40:53.522 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
00:40:53.522 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第2行宽度设置 ===
00:40:53.522 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
00:40:53.522 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:40:53.523 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列0 内容='[空]' 逻辑列0 跨度1 设置宽度: 180像素
00:40:53.523 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:40:53.523 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
00:40:53.523 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:40:53.524 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
00:40:53.524 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:40:53.524 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
00:40:53.524 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:40:53.524 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
00:40:53.525 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:40:53.525 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:40:53.525 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:40:53.525 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
00:40:53.525 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:40:53.526 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
00:40:53.526 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1778] - 列宽配置应用完成
00:40:53.535 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,977] - 应用表头合并单元格，数量: 7
00:40:53.535 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 7 个合并单元格，表头行数: 0
00:40:53.535 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 产品名称
00:40:53.536 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 规格型号
00:40:53.536 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 质量检验
00:40:53.537 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 生产信息
00:40:53.537 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='生产信息' 起始列3 结束列4 跨度2 实际行0
00:40:53.537 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列4 内容=''
00:40:53.538 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,5) -> (1,5) 调整后(0,5) -> (1,5) 内容: 检验员
00:40:53.538 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,6) -> (1,6) 调整后(0,6) -> (1,6) 内容: 审核员
00:40:53.538 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,7) -> (1,7) 调整后(0,7) -> (1,7) 内容: 负责人
00:40:53.538 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,985] - 应用数据行合并单元格，数量: 1，表头偏移: 2
00:40:53.538 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 1 个合并单元格，表头行数: 2
00:40:53.538 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2019] - === 表格结构信息 ===
00:40:53.538 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第0行，单元格数: 8
00:40:53.539 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='产品名称' 跨度=1 宽度=180像素(2700twips)
00:40:53.540 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='规格型号' 跨度=1 宽度=150像素(2250twips)
00:40:53.540 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='质量检验' 跨度=1 宽度=120像素(1800twips)
00:40:53.540 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='生产信息' 跨度=2 宽度=100像素(1500twips)
00:40:53.540 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:40:53.540 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='检验员' 跨度=1 宽度=90像素(1350twips)
00:40:53.541 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='审核员' 跨度=1 宽度=90像素(1350twips)
00:40:53.541 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='负责人' 跨度=1 宽度=90像素(1350twips)
00:40:53.541 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:40:53.541 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第1行，单元格数: 8
00:40:53.541 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='[空]' 跨度=1 宽度=180像素(2700twips)
00:40:53.541 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
00:40:53.541 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
00:40:53.542 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='批次号' 跨度=1 宽度=100像素(1500twips)
00:40:53.542 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='日期' 跨度=1 宽度=100像素(1500twips)
00:40:53.542 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:40:53.542 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:40:53.542 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:40:53.542 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:40:53.543 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第2行，单元格数: 8
00:40:53.543 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='智能手机\n（多功能检测）' 跨度=1 宽度=180像素(2700twips)
00:40:53.543 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
00:40:53.543 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
00:40:53.543 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:40:53.543 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:40:53.543 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:40:53.543 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:40:53.544 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:40:53.544 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:40:53.544 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2073] - === 表格结构信息结束 ===
00:40:53.550 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3055 bytes
00:40:53.553 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_004053.docx, 大小: 3055 bytes
00:55:30.910 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:55:30.913 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:55:37.373 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1007012 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:55:37.377 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:55:38.623 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:55:38.623 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:55:38.624 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:55:38.675 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:55:39.274 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:55:39.525 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:55:40.166 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:55:40.181 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.357 seconds (JVM running for 3.898)
00:56:11.124 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:56:11.207 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
00:56:11.207 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
00:56:11.208 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
00:56:11.208 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
00:56:11.208 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
00:56:11.472 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
00:56:11.514 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 3, 总列数: 8
00:56:11.595 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,972] - 应用表头合并单元格，数量: 7
00:56:11.596 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 7 个合并单元格，表头行数: 0
00:56:11.596 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 产品名称
00:56:11.598 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 规格型号
00:56:11.599 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 质量检验
00:56:11.599 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 生产信息
00:56:11.602 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='生产信息' 起始列3 结束列4 跨度2 实际行0
00:56:11.602 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列4 内容=''
00:56:11.602 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,5) -> (1,5) 调整后(0,5) -> (1,5) 内容: 检验员
00:56:11.603 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,6) -> (1,6) 调整后(0,6) -> (1,6) 内容: 审核员
00:56:11.603 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,7) -> (1,7) 调整后(0,7) -> (1,7) 内容: 负责人
00:56:11.603 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,980] - 应用数据行合并单元格，数量: 1，表头偏移: 2
00:56:11.603 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 1 个合并单元格，表头行数: 2
00:56:11.603 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1708] - 应用表头宽度配置: 列宽=[180, 150, 120, 100, 100, 90, 90, 90], 行高=[60, 40]
00:56:11.605 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1718] - 准备应用列宽配置，配置内容: [180, 150, 120, 100, 100, 90, 90, 90]
00:56:11.605 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1764] - 开始应用列宽配置: [180, 150, 120, 100, 100, 90, 90, 90]
00:56:11.605 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第0行宽度设置 ===
00:56:11.605 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
00:56:11.605 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:56:11.610 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列0 内容='产品名称' 逻辑列0 跨度1 设置宽度: 180像素
00:56:11.610 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:56:11.610 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列1 内容='规格型号' 逻辑列1 跨度1 设置宽度: 150像素
00:56:11.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:56:11.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列2 内容='质量检验' 逻辑列2 跨度1 设置宽度: 120像素
00:56:11.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1902] - 计算合并单元格宽度: 起始列3 结束列4 跨度2
00:56:11.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1908] -   + 逻辑列3 宽度: 100像素
00:56:11.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1908] -   + 逻辑列4 宽度: 100像素
00:56:11.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1914] - 合并单元格总宽度: 200像素
00:56:11.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列3 内容='生产信息' 逻辑列3 跨度2 设置宽度: 200像素
00:56:11.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:56:11.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列4 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:56:11.613 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:56:11.613 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列5 内容='检验员' 逻辑列6 跨度1 设置宽度: 90像素
00:56:11.613 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:56:11.614 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列6 内容='审核员' 逻辑列7 跨度1 设置宽度: 90像素
00:56:11.614 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第1行宽度设置 ===
00:56:11.614 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
00:56:11.615 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:56:11.615 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列0 内容='[空]' 逻辑列0 跨度1 设置宽度: 180像素
00:56:11.615 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:56:11.616 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
00:56:11.616 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:56:11.616 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
00:56:11.617 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:56:11.617 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列3 内容='批次号' 逻辑列3 跨度1 设置宽度: 100像素
00:56:11.618 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:56:11.618 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列4 内容='日期' 逻辑列4 跨度1 设置宽度: 100像素
00:56:11.618 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:56:11.619 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:56:11.619 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:56:11.619 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
00:56:11.619 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:56:11.620 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
00:56:11.620 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第2行宽度设置 ===
00:56:11.620 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
00:56:11.620 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:56:11.621 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列0 内容='智能手机\n（多功能...' 逻辑列0 跨度1 设置宽度: 180像素
00:56:11.621 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:56:11.622 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
00:56:11.622 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:56:11.622 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
00:56:11.622 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:56:11.622 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
00:56:11.623 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:56:11.623 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
00:56:11.623 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:56:11.623 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:56:11.623 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度90像素
00:56:11.624 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
00:56:11.624 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度90像素
00:56:11.624 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
00:56:11.624 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1778] - 列宽配置应用完成
00:56:11.624 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2019] - === 表格结构信息 ===
00:56:11.625 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第0行，单元格数: 8
00:56:11.625 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='产品名称' 跨度=1 宽度=180像素(2700twips)
00:56:11.626 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='规格型号' 跨度=1 宽度=150像素(2250twips)
00:56:11.626 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='质量检验' 跨度=1 宽度=120像素(1800twips)
00:56:11.626 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='生产信息' 跨度=2 宽度=200像素(3000twips)
00:56:11.626 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:56:11.627 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='检验员' 跨度=1 宽度=90像素(1350twips)
00:56:11.627 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='审核员' 跨度=1 宽度=90像素(1350twips)
00:56:11.628 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='负责人' 跨度=1 宽度=0像素(0twips)
00:56:11.628 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:56:11.628 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第1行，单元格数: 8
00:56:11.628 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='[空]' 跨度=1 宽度=180像素(2700twips)
00:56:11.628 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
00:56:11.629 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
00:56:11.629 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='批次号' 跨度=1 宽度=100像素(1500twips)
00:56:11.629 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='日期' 跨度=1 宽度=100像素(1500twips)
00:56:11.630 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:56:11.630 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:56:11.630 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:56:11.630 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:56:11.630 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第2行，单元格数: 8
00:56:11.631 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='智能手机\n（多功能检测）' 跨度=1 宽度=180像素(2700twips)
00:56:11.631 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
00:56:11.631 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
00:56:11.631 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:56:11.631 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:56:11.632 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:56:11.632 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:56:11.632 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:56:11.632 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:56:11.632 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2073] - === 表格结构信息结束 ===
00:56:11.695 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3063 bytes
00:56:11.707 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_005611.docx, 大小: 3063 bytes
00:58:42.656 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
00:58:42.656 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
00:58:42.657 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
00:58:42.657 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
00:58:42.657 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
00:58:42.659 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
00:58:42.660 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 3, 总列数: 8
00:58:42.671 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,972] - 应用表头合并单元格，数量: 5
00:58:42.671 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 5 个合并单元格，表头行数: 0
00:58:42.671 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 产品名称
00:58:42.672 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,1) -> (0,2) 调整后(0,1) -> (0,2) 内容: 生产信息
00:58:42.673 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='生产信息' 起始列1 结束列2 跨度2 实际行0
00:58:42.673 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列2 内容=''
00:58:42.673 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,3) -> (1,3) 调整后(0,3) -> (1,3) 内容: 检验员
00:58:42.673 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,4) -> (1,4) 调整后(0,4) -> (1,4) 内容: 审核员
00:58:42.673 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,5) -> (1,5) 调整后(0,5) -> (1,5) 内容: 负责人
00:58:42.673 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,980] - 应用数据行合并单元格，数量: 1，表头偏移: 2
00:58:42.673 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 1 个合并单元格，表头行数: 2
00:58:42.673 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1708] - 应用表头宽度配置: 列宽=[180, 150, 120, 100, 100, 90], 行高=[60, 40]
00:58:42.674 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1718] - 准备应用列宽配置，配置内容: [180, 150, 120, 100, 100, 90]
00:58:42.674 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1764] - 开始应用列宽配置: [180, 150, 120, 100, 100, 90]
00:58:42.674 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第0行宽度设置 ===
00:58:42.674 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 6
00:58:42.674 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:58:42.674 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列0 内容='产品名称' 逻辑列0 跨度1 设置宽度: 180像素
00:58:42.674 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1902] - 计算合并单元格宽度: 起始列1 结束列2 跨度2
00:58:42.674 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1908] -   + 逻辑列1 宽度: 150像素
00:58:42.675 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1908] -   + 逻辑列2 宽度: 120像素
00:58:42.675 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1914] - 合并单元格总宽度: 270像素
00:58:42.675 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列1 内容='生产信息' 逻辑列1 跨度2 设置宽度: 270像素
00:58:42.675 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:58:42.675 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列2 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
00:58:42.676 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:58:42.676 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列3 内容='检验员' 逻辑列4 跨度1 设置宽度: 100像素
00:58:42.676 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:58:42.677 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列4 内容='审核员' 逻辑列5 跨度1 设置宽度: 90像素
00:58:42.677 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第1行宽度设置 ===
00:58:42.677 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 6
00:58:42.677 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:58:42.677 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列0 内容='[空]' 逻辑列0 跨度1 设置宽度: 180像素
00:58:42.677 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:58:42.677 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列1 内容='批次号' 逻辑列1 跨度1 设置宽度: 150像素
00:58:42.677 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:58:42.678 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列2 内容='日期' 逻辑列2 跨度1 设置宽度: 120像素
00:58:42.678 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:58:42.678 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
00:58:42.678 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:58:42.678 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
00:58:42.678 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:58:42.678 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:58:42.678 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第2行宽度设置 ===
00:58:42.679 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 6
00:58:42.679 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度180像素
00:58:42.679 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列0 内容='智能手机\n（多功能...' 逻辑列0 跨度1 设置宽度: 180像素
00:58:42.679 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度150像素
00:58:42.679 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
00:58:42.679 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度120像素
00:58:42.680 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
00:58:42.680 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度100像素
00:58:42.680 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
00:58:42.680 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度100像素
00:58:42.680 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
00:58:42.680 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度90像素
00:58:42.680 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
00:58:42.681 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1778] - 列宽配置应用完成
00:58:42.681 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2019] - === 表格结构信息 ===
00:58:42.681 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第0行，单元格数: 8
00:58:42.682 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='产品名称' 跨度=1 宽度=180像素(2700twips)
00:58:42.682 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='生产信息' 跨度=2 宽度=270像素(4050twips)
00:58:42.682 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:58:42.683 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='检验员' 跨度=1 宽度=100像素(1500twips)
00:58:42.683 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='审核员' 跨度=1 宽度=90像素(1350twips)
00:58:42.683 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='负责人' 跨度=1 宽度=0像素(0twips)
00:58:42.683 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:58:42.683 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:58:42.683 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:58:42.684 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第1行，单元格数: 8
00:58:42.684 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='[空]' 跨度=1 宽度=180像素(2700twips)
00:58:42.684 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='批次号' 跨度=1 宽度=150像素(2250twips)
00:58:42.684 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='日期' 跨度=1 宽度=120像素(1800twips)
00:58:42.684 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:58:42.684 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:58:42.684 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:58:42.685 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:58:42.685 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:58:42.685 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:58:42.685 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第2行，单元格数: 8
00:58:42.685 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='智能手机\n（多功能检测）' 跨度=1 宽度=180像素(2700twips)
00:58:42.685 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
00:58:42.685 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
00:58:42.685 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:58:42.686 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
00:58:42.686 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
00:58:42.686 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:58:42.686 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='[空]' 跨度=1 宽度=0像素(0twips)
00:58:42.686 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
00:58:42.686 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2073] - === 表格结构信息结束 ===
00:58:42.692 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3019 bytes
00:58:42.696 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_005842.docx, 大小: 3019 bytes
01:03:44.763 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
01:03:44.763 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
01:03:44.764 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 4, 合并单元格数量: 1
01:03:44.764 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
01:03:44.764 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
01:03:44.765 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
01:03:44.765 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 6, 总列数: 8
01:03:44.773 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,680] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi><mo>=</mo><mrow><mi>m</mi><mo>&#x2062;</mo><msup><mi>c</mi><mn>2</mn></msup></mrow></math>}
01:03:44.775 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:03:45.043 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,680] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>&#x3C0;</mi><mo>&#x2062;</mo><msup><mi>r</mi><mn>2</mn></msup></math>}
01:03:45.043 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:03:45.120 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,680] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><msub><mi>x</mi><mi>i</mi></msub></math>}
01:03:45.121 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:03:45.203 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,680] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msubsup><mo>&#x222B;</mo><mn>0</mn><mn>1</mn></msubsup><mi>x</mi><mrow><mi>d</mi><mi>x</mi></mrow></mrow><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math>}
01:03:45.203 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:03:45.310 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,972] - 应用表头合并单元格，数量: 7
01:03:45.311 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 7 个合并单元格，表头行数: 0
01:03:45.311 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 检查项目
01:03:45.311 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 技术要求
01:03:45.311 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 检查结果
01:03:45.311 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 完工
01:03:45.312 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='完工' 起始列3 结束列4 跨度2 实际行0
01:03:45.312 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列4 内容=''
01:03:45.312 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,5) -> (1,5) 调整后(0,5) -> (1,5) 内容: 检查员
01:03:45.312 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,6) -> (1,6) 调整后(0,6) -> (1,6) 内容: 组长
01:03:45.312 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,7) -> (1,7) 调整后(0,7) -> (1,7) 内容: 检验员
01:03:45.313 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,980] - 应用数据行合并单元格，数量: 1，表头偏移: 2
01:03:45.313 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 1 个合并单元格，表头行数: 2
01:03:45.313 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(2,3) -> (3,4) 调整后(4,3) -> (5,4) 内容: 12月17-18日
01:03:45.313 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='12月17-18日' 起始列3 结束列4 跨度2 实际行4
01:03:45.314 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行4 列4 内容='17'
01:03:45.314 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1708] - 应用表头宽度配置: 列宽=[150, 200, 150, 50, 50, 80, 80, 80], 行高=[50, 50]
01:03:45.314 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1718] - 准备应用列宽配置，配置内容: [150, 200, 150, 50, 50, 80, 80, 80]
01:03:45.314 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1764] - 开始应用列宽配置: [150, 200, 150, 50, 50, 80, 80, 80]
01:03:45.314 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第0行宽度设置 ===
01:03:45.314 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
01:03:45.314 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度150像素
01:03:45.315 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列0 内容='检查项目' 逻辑列0 跨度1 设置宽度: 150像素
01:03:45.315 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度200像素
01:03:45.315 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列1 内容='技术要求' 逻辑列1 跨度1 设置宽度: 200像素
01:03:45.315 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度150像素
01:03:45.315 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列2 内容='检查结果' 逻辑列2 跨度1 设置宽度: 150像素
01:03:45.315 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1902] - 计算合并单元格宽度: 起始列3 结束列4 跨度2
01:03:45.315 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1908] -   + 逻辑列3 宽度: 50像素
01:03:45.315 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1908] -   + 逻辑列4 宽度: 50像素
01:03:45.316 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1914] - 合并单元格总宽度: 100像素
01:03:45.316 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列3 内容='完工' 逻辑列3 跨度2 设置宽度: 100像素
01:03:45.316 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度80像素
01:03:45.316 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列4 内容='[空]' 逻辑列5 跨度1 设置宽度: 80像素
01:03:45.316 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度80像素
01:03:45.316 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列5 内容='检查员' 逻辑列6 跨度1 设置宽度: 80像素
01:03:45.316 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度80像素
01:03:45.316 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行0 列6 内容='组长' 逻辑列7 跨度1 设置宽度: 80像素
01:03:45.317 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第1行宽度设置 ===
01:03:45.317 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
01:03:45.317 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度150像素
01:03:45.317 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列0 内容='[空]' 逻辑列0 跨度1 设置宽度: 150像素
01:03:45.317 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度200像素
01:03:45.317 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 200像素
01:03:45.317 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度150像素
01:03:45.317 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 150像素
01:03:45.317 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度50像素
01:03:45.317 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列3 内容='月' 逻辑列3 跨度1 设置宽度: 50像素
01:03:45.318 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度50像素
01:03:45.318 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列4 内容='日' 逻辑列4 跨度1 设置宽度: 50像素
01:03:45.318 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度80像素
01:03:45.318 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 80像素
01:03:45.318 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度80像素
01:03:45.318 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 80像素
01:03:45.318 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度80像素
01:03:45.318 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行1 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 80像素
01:03:45.318 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第2行宽度设置 ===
01:03:45.319 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
01:03:45.319 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度150像素
01:03:45.319 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列0 内容='数学公式测试' 逻辑列0 跨度1 设置宽度: 150像素
01:03:45.319 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度200像素
01:03:45.319 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 200像素
01:03:45.319 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度150像素
01:03:45.319 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列2 内容='合格' 逻辑列2 跨度1 设置宽度: 150像素
01:03:45.319 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度50像素
01:03:45.320 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列3 内容='12月17-18日' 逻辑列3 跨度1 设置宽度: 50像素
01:03:45.320 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度50像素
01:03:45.320 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列4 内容='15' 逻辑列4 跨度1 设置宽度: 50像素
01:03:45.320 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度80像素
01:03:45.320 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列5 内容='张三' 逻辑列5 跨度1 设置宽度: 80像素
01:03:45.320 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度80像素
01:03:45.320 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列6 内容='李四' 逻辑列6 跨度1 设置宽度: 80像素
01:03:45.320 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度80像素
01:03:45.321 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行2 列7 内容='王五' 逻辑列7 跨度1 设置宽度: 80像素
01:03:45.321 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第3行宽度设置 ===
01:03:45.321 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
01:03:45.321 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度150像素
01:03:45.321 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行3 列0 内容='几何计算' 逻辑列0 跨度1 设置宽度: 150像素
01:03:45.321 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度200像素
01:03:45.321 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行3 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 200像素
01:03:45.322 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度150像素
01:03:45.322 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行3 列2 内容='合格' 逻辑列2 跨度1 设置宽度: 150像素
01:03:45.322 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度50像素
01:03:45.322 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行3 列3 内容='12' 逻辑列3 跨度1 设置宽度: 50像素
01:03:45.322 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度50像素
01:03:45.322 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行3 列4 内容='16' 逻辑列4 跨度1 设置宽度: 50像素
01:03:45.322 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度80像素
01:03:45.322 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行3 列5 内容='张三' 逻辑列5 跨度1 设置宽度: 80像素
01:03:45.323 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度80像素
01:03:45.323 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行3 列6 内容='李四' 逻辑列6 跨度1 设置宽度: 80像素
01:03:45.323 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度80像素
01:03:45.323 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行3 列7 内容='王五' 逻辑列7 跨度1 设置宽度: 80像素
01:03:45.323 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第4行宽度设置 ===
01:03:45.323 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
01:03:45.323 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度150像素
01:03:45.324 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行4 列0 内容='统计分析' 逻辑列0 跨度1 设置宽度: 150像素
01:03:45.324 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度200像素
01:03:45.324 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行4 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 200像素
01:03:45.324 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度150像素
01:03:45.324 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行4 列2 内容='合格' 逻辑列2 跨度1 设置宽度: 150像素
01:03:45.325 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1902] - 计算合并单元格宽度: 起始列3 结束列4 跨度2
01:03:45.325 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1908] -   + 逻辑列3 宽度: 50像素
01:03:45.325 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1908] -   + 逻辑列4 宽度: 50像素
01:03:45.325 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1914] - 合并单元格总宽度: 100像素
01:03:45.325 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行4 列3 内容='12月17-18日' 逻辑列3 跨度2 设置宽度: 100像素
01:03:45.325 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度80像素
01:03:45.325 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行4 列4 内容='17' 逻辑列5 跨度1 设置宽度: 80像素
01:03:45.326 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度80像素
01:03:45.326 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行4 列5 内容='张三' 逻辑列6 跨度1 设置宽度: 80像素
01:03:45.326 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度80像素
01:03:45.326 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行4 列6 内容='李四' 逻辑列7 跨度1 设置宽度: 80像素
01:03:45.326 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1793] - === 处理第5行宽度设置 ===
01:03:45.326 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1794] - 物理单元格数: 8, 逻辑列数: 8
01:03:45.326 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列0 宽度150像素
01:03:45.326 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行5 列0 内容='积分计算' 逻辑列0 跨度1 设置宽度: 150像素
01:03:45.326 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列1 宽度200像素
01:03:45.327 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行5 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 200像素
01:03:45.327 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列2 宽度150像素
01:03:45.327 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行5 列2 内容='合格' 逻辑列2 跨度1 设置宽度: 150像素
01:03:45.327 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列3 宽度50像素
01:03:45.327 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行5 列3 内容='12' 逻辑列3 跨度1 设置宽度: 50像素
01:03:45.327 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列4 宽度50像素
01:03:45.327 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行5 列4 内容='18' 逻辑列4 跨度1 设置宽度: 50像素
01:03:45.327 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列5 宽度80像素
01:03:45.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行5 列5 内容='张三' 逻辑列5 跨度1 设置宽度: 80像素
01:03:45.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列6 宽度80像素
01:03:45.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行5 列6 内容='李四' 逻辑列6 跨度1 设置宽度: 80像素
01:03:45.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,1920] - 非合并单元格宽度: 逻辑列7 宽度80像素
01:03:45.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1819] - 行5 列7 内容='王五' 逻辑列7 跨度1 设置宽度: 80像素
01:03:45.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1778] - 列宽配置应用完成
01:03:45.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2019] - === 表格结构信息 ===
01:03:45.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第0行，单元格数: 8
01:03:45.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='检查项目' 跨度=1 宽度=150像素(2250twips)
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='技术要求' 跨度=1 宽度=200像素(3000twips)
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='检查结果' 跨度=1 宽度=150像素(2250twips)
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='完工' 跨度=2 宽度=100像素(1500twips)
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='[空]' 跨度=1 宽度=80像素(1200twips)
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='检查员' 跨度=1 宽度=80像素(1200twips)
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='组长' 跨度=1 宽度=80像素(1200twips)
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='检验员' 跨度=1 宽度=0像素(0twips)
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第1行，单元格数: 8
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
01:03:45.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=200像素(3000twips)
01:03:45.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
01:03:45.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='月' 跨度=1 宽度=50像素(750twips)
01:03:45.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='日' 跨度=1 宽度=50像素(750twips)
01:03:45.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='[空]' 跨度=1 宽度=80像素(1200twips)
01:03:45.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='[空]' 跨度=1 宽度=80像素(1200twips)
01:03:45.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='[空]' 跨度=1 宽度=80像素(1200twips)
01:03:45.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
01:03:45.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第2行，单元格数: 8
01:03:45.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='数学公式测试' 跨度=1 宽度=150像素(2250twips)
01:03:45.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=200像素(3000twips)
01:03:45.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='合格' 跨度=1 宽度=150像素(2250twips)
01:03:45.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='12月17-18日' 跨度=1 宽度=50像素(750twips)
01:03:45.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='15' 跨度=1 宽度=50像素(750twips)
01:03:45.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='张三' 跨度=1 宽度=80像素(1200twips)
01:03:45.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='李四' 跨度=1 宽度=80像素(1200twips)
01:03:45.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='王五' 跨度=1 宽度=80像素(1200twips)
01:03:45.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
01:03:45.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第3行，单元格数: 8
01:03:45.332 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='几何计算' 跨度=1 宽度=150像素(2250twips)
01:03:45.332 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=200像素(3000twips)
01:03:45.332 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='合格' 跨度=1 宽度=150像素(2250twips)
01:03:45.332 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='12' 跨度=1 宽度=50像素(750twips)
01:03:45.332 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='16' 跨度=1 宽度=50像素(750twips)
01:03:45.332 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='张三' 跨度=1 宽度=80像素(1200twips)
01:03:45.332 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='李四' 跨度=1 宽度=80像素(1200twips)
01:03:45.332 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='王五' 跨度=1 宽度=80像素(1200twips)
01:03:45.332 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
01:03:45.332 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第4行，单元格数: 8
01:03:45.333 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='统计分析' 跨度=1 宽度=150像素(2250twips)
01:03:45.333 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=200像素(3000twips)
01:03:45.333 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='合格' 跨度=1 宽度=150像素(2250twips)
01:03:45.333 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='12月17-18日' 跨度=2 宽度=100像素(1500twips)
01:03:45.333 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='17' 跨度=1 宽度=80像素(1200twips)
01:03:45.333 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='张三' 跨度=1 宽度=80像素(1200twips)
01:03:45.333 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='李四' 跨度=1 宽度=80像素(1200twips)
01:03:45.333 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='王五' 跨度=1 宽度=0像素(0twips)
01:03:45.333 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
01:03:45.333 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2026] - 第5行，单元格数: 8
01:03:45.334 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列0: 内容='积分计算' 跨度=1 宽度=150像素(2250twips)
01:03:45.334 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列1: 内容='[空]' 跨度=1 宽度=200像素(3000twips)
01:03:45.334 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列2: 内容='合格' 跨度=1 宽度=150像素(2250twips)
01:03:45.334 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列3: 内容='12' 跨度=1 宽度=50像素(750twips)
01:03:45.334 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列4: 内容='18' 跨度=1 宽度=50像素(750twips)
01:03:45.335 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列5: 内容='张三' 跨度=1 宽度=80像素(1200twips)
01:03:45.336 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列6: 内容='李四' 跨度=1 宽度=80像素(1200twips)
01:03:45.336 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2066] -   列7: 内容='王五' 跨度=1 宽度=80像素(1200twips)
01:03:45.336 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2070] - 
01:03:45.336 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [printTableStructure,2073] - === 表格结构信息结束 ===
01:03:45.343 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3607 bytes
01:03:45.346 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_010345.docx, 大小: 3607 bytes
01:09:33.458 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
01:09:33.461 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
01:09:39.355 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1023627 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
01:09:39.358 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
01:09:40.262 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
01:09:40.262 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
01:09:40.263 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
01:09:40.304 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
01:09:40.820 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
01:09:41.064 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
01:09:41.723 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
01:09:41.738 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.792 seconds (JVM running for 3.181)
01:09:48.112 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:09:48.199 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
01:09:48.199 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
01:09:48.200 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
01:09:48.200 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
01:09:48.200 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
01:09:48.464 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
01:09:48.503 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 3, 总列数: 8
01:09:48.577 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,972] - 应用表头合并单元格，数量: 5
01:09:48.578 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 5 个合并单元格，表头行数: 0
01:09:48.578 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 产品名称
01:09:48.581 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 规格型号
01:09:48.581 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 质量检验
01:09:48.581 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 生产信息
01:09:48.583 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='生产信息' 起始列3 结束列4 跨度2 实际行0
01:09:48.584 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列4 内容=''
01:09:48.584 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,5) -> (0,7) 调整后(0,5) -> (0,7) 内容: 责任人员
01:09:48.584 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='责任人员' 起始列5 结束列7 跨度3 实际行0
01:09:48.585 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列6 内容=''
01:09:48.585 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列7 内容=''
01:09:48.585 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,980] - 应用数据行合并单元格，数量: 1，表头偏移: 2
01:09:48.585 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 1 个合并单元格，表头行数: 2
01:09:48.586 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1708] - 应用表头宽度配置: 列宽=[180, 150, 120, 100, 100, 90, 90, 90], 行高=[60, 40]
01:09:48.588 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1718] - 准备应用列宽配置，配置内容: [180, 150, 120, 100, 100, 90, 90, 90]
01:09:48.589 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1764] - 开始应用列宽配置: [180, 150, 120, 100, 100, 90, 90, 90]
01:09:48.589 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1799] - === 处理第0行宽度设置 ===
01:09:48.589 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1800] - 物理单元格数: 8, 逻辑列数: 8
01:09:48.589 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列0 宽度180像素
01:09:48.596 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列0 内容='产品名称' 逻辑列0 跨度1 设置宽度: 180像素
01:09:48.597 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列1 宽度150像素
01:09:48.597 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列1 内容='规格型号' 逻辑列1 跨度1 设置宽度: 150像素
01:09:48.598 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列2 宽度120像素
01:09:48.598 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列2 内容='质量检验' 逻辑列2 跨度1 设置宽度: 120像素
01:09:48.598 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2023] - 计算合并单元格宽度: 起始列3 结束列4 跨度2
01:09:48.599 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2029] -   + 逻辑列3 宽度: 100像素
01:09:48.599 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2029] -   + 逻辑列4 宽度: 100像素
01:09:48.599 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2035] - 合并单元格总宽度: 200像素
01:09:48.599 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列3 内容='生产信息' 逻辑列3 跨度2 设置宽度: 200像素
01:09:48.599 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列5 宽度90像素
01:09:48.599 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列4 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
01:09:48.600 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2023] - 计算合并单元格宽度: 起始列6 结束列7 跨度3
01:09:48.600 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2029] -   + 逻辑列6 宽度: 90像素
01:09:48.600 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2029] -   + 逻辑列7 宽度: 90像素
01:09:48.600 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2035] - 合并单元格总宽度: 180像素
01:09:48.600 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列5 内容='责任人员' 逻辑列6 跨度3 设置宽度: 180像素
01:09:48.601 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1854] - === 处理表头第二行1宽度设置（特殊对齐处理） ===
01:09:48.601 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1855] - 物理单元格数: 8, 逻辑列数: 8
01:09:48.601 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1866] - 发现第一行合并: 起始列3 跨度2
01:09:48.601 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1866] - 发现第一行合并: 起始列5 跨度3
01:09:48.602 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1924] - 普通单元格: 逻辑列0 宽度180
01:09:48.602 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列0 内容='[空]' 逻辑列0 最终宽度: 180像素
01:09:48.602 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1924] - 普通单元格: 逻辑列1 宽度150
01:09:48.602 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列1 内容='[空]' 逻辑列1 最终宽度: 150像素
01:09:48.603 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1924] - 普通单元格: 逻辑列2 宽度120
01:09:48.603 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列2 内容='[空]' 逻辑列2 最终宽度: 120像素
01:09:48.603 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1913] - 合并单元格下方主单元格: 逻辑列3 相对位置0 总宽度200
01:09:48.603 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列3 内容='批次号' 逻辑列3 最终宽度: 200像素
01:09:48.603 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1918] - 合并单元格下方子单元格: 逻辑列4 相对位置1 宽度100
01:09:48.604 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列4 内容='日期' 逻辑列4 最终宽度: 100像素
01:09:48.604 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1913] - 合并单元格下方主单元格: 逻辑列5 相对位置0 总宽度270
01:09:48.604 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列5 内容='检验员' 逻辑列5 最终宽度: 270像素
01:09:48.604 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1918] - 合并单元格下方子单元格: 逻辑列6 相对位置1 宽度90
01:09:48.604 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列6 内容='审核员' 逻辑列6 最终宽度: 90像素
01:09:48.605 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1918] - 合并单元格下方子单元格: 逻辑列7 相对位置2 宽度90
01:09:48.605 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列7 内容='负责人' 逻辑列7 最终宽度: 90像素
01:09:48.605 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1799] - === 处理第2行宽度设置 ===
01:09:48.605 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1800] - 物理单元格数: 8, 逻辑列数: 8
01:09:48.605 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列0 宽度180像素
01:09:48.606 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列0 内容='智能手机\n（多功能...' 逻辑列0 跨度1 设置宽度: 180像素
01:09:48.606 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列1 宽度150像素
01:09:48.606 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
01:09:48.606 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列2 宽度120像素
01:09:48.606 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
01:09:48.606 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列3 宽度100像素
01:09:48.606 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
01:09:48.607 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列4 宽度100像素
01:09:48.607 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
01:09:48.607 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列5 宽度90像素
01:09:48.607 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
01:09:48.607 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列6 宽度90像素
01:09:48.607 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
01:09:48.607 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2041] - 非合并单元格宽度: 逻辑列7 宽度90像素
01:09:48.608 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
01:09:48.608 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1784] - 列宽配置应用完成
01:09:48.608 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2140] - === 表格结构信息 ===
01:09:48.608 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2147] - 第0行，单元格数: 8
01:09:48.608 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列0: 内容='产品名称' 跨度=1 宽度=180像素(2700twips)
01:09:48.608 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列1: 内容='规格型号' 跨度=1 宽度=150像素(2250twips)
01:09:48.608 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列2: 内容='质量检验' 跨度=1 宽度=120像素(1800twips)
01:09:48.609 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列3: 内容='生产信息' 跨度=2 宽度=200像素(3000twips)
01:09:48.609 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列4: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:09:48.609 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列5: 内容='责任人员' 跨度=3 宽度=180像素(2700twips)
01:09:48.609 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列6: 内容='[空]' 跨度=1 宽度=0像素(0twips)
01:09:48.609 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列7: 内容='[空]' 跨度=1 宽度=0像素(0twips)
01:09:48.609 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2191] - 
01:09:48.609 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2147] - 第1行，单元格数: 8
01:09:48.609 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列0: 内容='[空]' 跨度=1 宽度=180像素(2700twips)
01:09:48.610 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
01:09:48.610 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
01:09:48.610 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列3: 内容='批次号' 跨度=1 宽度=200像素(3000twips)
01:09:48.610 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列4: 内容='日期' 跨度=1 宽度=100像素(1500twips)
01:09:48.610 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列5: 内容='检验员' 跨度=1 宽度=270像素(4050twips)
01:09:48.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列6: 内容='审核员' 跨度=1 宽度=90像素(1350twips)
01:09:48.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列7: 内容='负责人' 跨度=1 宽度=90像素(1350twips)
01:09:48.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2191] - 
01:09:48.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2147] - 第2行，单元格数: 8
01:09:48.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列0: 内容='智能手机\n（多功能检测）' 跨度=1 宽度=180像素(2700twips)
01:09:48.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
01:09:48.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
01:09:48.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列3: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
01:09:48.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列4: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
01:09:48.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列5: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:09:48.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列6: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:09:48.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2187] -   列7: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:09:48.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2191] - 
01:09:48.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2194] - === 表格结构信息结束 ===
01:09:48.670 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3084 bytes
01:09:48.682 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_010948.docx, 大小: 3084 bytes
01:21:12.421 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
01:21:12.424 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
01:21:17.640 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1037214 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
01:21:17.643 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
01:21:18.451 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
01:21:18.452 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
01:21:18.452 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
01:21:18.486 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
01:21:18.959 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
01:21:19.194 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
01:21:19.842 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
01:21:19.858 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.611 seconds (JVM running for 3.012)
01:21:23.564 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:21:23.654 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
01:21:23.655 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
01:21:23.655 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
01:21:23.656 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
01:21:23.656 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
01:21:23.947 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
01:21:23.986 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 3, 总列数: 8
01:21:24.065 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,972] - 应用表头合并单元格，数量: 5
01:21:24.065 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 5 个合并单元格，表头行数: 0
01:21:24.065 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 产品名称
01:21:24.068 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 规格型号
01:21:24.069 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 质量检验
01:21:24.069 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 生产信息
01:21:24.071 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='生产信息' 起始列3 结束列4 跨度2 实际行0
01:21:24.072 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列4 内容=''
01:21:24.072 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,5) -> (0,7) 调整后(0,5) -> (0,7) 内容: 责任人员
01:21:24.073 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='责任人员' 起始列5 结束列7 跨度3 实际行0
01:21:24.074 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列6 内容=''
01:21:24.074 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列7 内容=''
01:21:24.074 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,980] - 应用数据行合并单元格，数量: 1，表头偏移: 2
01:21:24.074 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 1 个合并单元格，表头行数: 2
01:21:24.075 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1708] - 应用表头宽度配置: 列宽=[180, 150, 120, 100, 100, 90, 90, 90], 行高=[60, 40]
01:21:24.077 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1718] - 准备应用列宽配置，配置内容: [180, 150, 120, 100, 100, 90, 90, 90]
01:21:24.077 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1764] - 开始应用列宽配置: [180, 150, 120, 100, 100, 90, 90, 90]
01:21:24.077 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1799] - === 处理第0行宽度设置 ===
01:21:24.078 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1800] - 物理单元格数: 8, 逻辑列数: 8
01:21:24.078 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列0 宽度180像素
01:21:24.082 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列0 内容='产品名称' 逻辑列0 跨度1 设置宽度: 180像素
01:21:24.083 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列1 宽度150像素
01:21:24.083 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列1 内容='规格型号' 逻辑列1 跨度1 设置宽度: 150像素
01:21:24.084 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列2 宽度120像素
01:21:24.084 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列2 内容='质量检验' 逻辑列2 跨度1 设置宽度: 120像素
01:21:24.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2042] - 计算合并单元格宽度: 起始列3 结束列4 跨度2
01:21:24.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2048] -   + 逻辑列3 宽度: 100像素
01:21:24.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2048] -   + 逻辑列4 宽度: 100像素
01:21:24.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2054] - 合并单元格总宽度: 200像素
01:21:24.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列3 内容='生产信息' 逻辑列3 跨度2 设置宽度: 200像素
01:21:24.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列5 宽度90像素
01:21:24.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列4 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
01:21:24.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2042] - 计算合并单元格宽度: 起始列6 结束列7 跨度3
01:21:24.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2048] -   + 逻辑列6 宽度: 90像素
01:21:24.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2048] -   + 逻辑列7 宽度: 90像素
01:21:24.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2054] - 合并单元格总宽度: 180像素
01:21:24.087 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行0 列5 内容='责任人员' 逻辑列6 跨度3 设置宽度: 180像素
01:21:24.087 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1854] - === 处理表头第二行1宽度设置（特殊对齐处理） ===
01:21:24.087 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1855] - 物理单元格数: 8, 逻辑列数: 8
01:21:24.087 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1866] - 发现第一行合并: 起始列3 跨度2
01:21:24.087 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1866] - 发现第一行合并: 起始列5 跨度3
01:21:24.087 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1924] - 普通单元格: 逻辑列0 宽度180
01:21:24.088 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列0 内容='[空]' 逻辑列0 最终宽度: 180像素
01:21:24.088 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1924] - 普通单元格: 逻辑列1 宽度150
01:21:24.088 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列1 内容='[空]' 逻辑列1 最终宽度: 150像素
01:21:24.088 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1924] - 普通单元格: 逻辑列2 宽度120
01:21:24.088 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列2 内容='[空]' 逻辑列2 最终宽度: 120像素
01:21:24.088 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1913] - 合并单元格下方主单元格: 逻辑列3 相对位置0 总宽度200
01:21:24.089 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列3 内容='批次号' 逻辑列3 最终宽度: 200像素
01:21:24.089 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1963] - 跳过合并单元格范围: 逻辑列3 跳过2列
01:21:24.089 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1913] - 合并单元格下方主单元格: 逻辑列5 相对位置0 总宽度270
01:21:24.089 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1944] - 行1 列4 内容='日期' 逻辑列5 最终宽度: 270像素
01:21:24.090 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1963] - 跳过合并单元格范围: 逻辑列5 跳过3列
01:21:24.091 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1799] - === 处理第2行宽度设置 ===
01:21:24.091 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1800] - 物理单元格数: 8, 逻辑列数: 8
01:21:24.091 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列0 宽度180像素
01:21:24.091 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列0 内容='智能手机\n（多功能...' 逻辑列0 跨度1 设置宽度: 180像素
01:21:24.092 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列1 宽度150像素
01:21:24.092 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
01:21:24.092 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列2 宽度120像素
01:21:24.092 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
01:21:24.092 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列3 宽度100像素
01:21:24.092 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
01:21:24.092 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列4 宽度100像素
01:21:24.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
01:21:24.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列5 宽度90像素
01:21:24.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
01:21:24.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列6 宽度90像素
01:21:24.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
01:21:24.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2060] - 非合并单元格宽度: 逻辑列7 宽度90像素
01:21:24.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1825] - 行2 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
01:21:24.094 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1784] - 列宽配置应用完成
01:21:24.094 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2159] - === 表格结构信息 ===
01:21:24.094 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2166] - 第0行，单元格数: 8
01:21:24.094 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列0: 内容='产品名称' 跨度=1 宽度=180像素(2700twips)
01:21:24.094 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列1: 内容='规格型号' 跨度=1 宽度=150像素(2250twips)
01:21:24.095 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列2: 内容='质量检验' 跨度=1 宽度=120像素(1800twips)
01:21:24.095 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列3: 内容='生产信息' 跨度=2 宽度=200像素(3000twips)
01:21:24.095 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列4: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:21:24.095 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列5: 内容='责任人员' 跨度=3 宽度=180像素(2700twips)
01:21:24.095 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列6: 内容='[空]' 跨度=1 宽度=0像素(0twips)
01:21:24.095 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列7: 内容='[空]' 跨度=1 宽度=0像素(0twips)
01:21:24.095 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2210] - 
01:21:24.096 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2166] - 第1行，单元格数: 8
01:21:24.096 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列0: 内容='[空]' 跨度=1 宽度=180像素(2700twips)
01:21:24.096 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
01:21:24.096 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
01:21:24.096 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列3: 内容='批次号' 跨度=1 宽度=200像素(3000twips)
01:21:24.096 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列4: 内容='日期' 跨度=1 宽度=270像素(4050twips)
01:21:24.096 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列5: 内容='检验员' 跨度=1 宽度=0像素(0twips)
01:21:24.097 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列6: 内容='审核员' 跨度=1 宽度=0像素(0twips)
01:21:24.097 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列7: 内容='负责人' 跨度=1 宽度=0像素(0twips)
01:21:24.097 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2210] - 
01:21:24.098 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2166] - 第2行，单元格数: 8
01:21:24.098 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列0: 内容='智能手机\n（多功能检测）' 跨度=1 宽度=180像素(2700twips)
01:21:24.098 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
01:21:24.099 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
01:21:24.099 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列3: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
01:21:24.099 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列4: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
01:21:24.099 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列5: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:21:24.099 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列6: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:21:24.099 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2206] -   列7: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:21:24.100 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2210] - 
01:21:24.100 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2213] - === 表格结构信息结束 ===
01:21:24.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3083 bytes
01:21:24.173 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_012124.docx, 大小: 3083 bytes
01:24:46.482 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
01:24:46.485 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
01:24:51.766 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1041523 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
01:24:51.770 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
01:24:52.669 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
01:24:52.670 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
01:24:52.671 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
01:24:52.725 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
01:24:53.220 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
01:24:53.447 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
01:24:54.067 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
01:24:54.082 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.69 seconds (JVM running for 3.083)
01:24:56.980 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:24:57.079 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
01:24:57.079 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
01:24:57.080 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
01:24:57.080 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
01:24:57.080 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
01:24:57.387 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
01:24:57.433 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 3, 总列数: 8
01:24:57.525 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,972] - 应用表头合并单元格，数量: 5
01:24:57.525 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 5 个合并单元格，表头行数: 0
01:24:57.525 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 产品名称
01:24:57.528 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 规格型号
01:24:57.529 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 质量检验
01:24:57.529 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 生产信息
01:24:57.531 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='生产信息' 起始列3 结束列4 跨度2 实际行0
01:24:57.531 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列4 内容=''
01:24:57.532 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,5) -> (0,7) 调整后(0,5) -> (0,7) 内容: 责任人员
01:24:57.532 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='责任人员' 起始列5 结束列7 跨度3 实际行0
01:24:57.532 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列6 内容=''
01:24:57.532 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列7 内容=''
01:24:57.532 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,980] - 应用数据行合并单元格，数量: 1，表头偏移: 2
01:24:57.532 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 1 个合并单元格，表头行数: 2
01:24:57.533 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1708] - 应用表头宽度配置: 列宽=[180, 150, 120, 100, 100, 90, 90, 90], 行高=[60, 40]
01:24:57.535 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1718] - 准备应用列宽配置，配置内容: [180, 150, 120, 100, 100, 90, 90, 90]
01:24:57.535 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1764] - 开始应用列宽配置: [180, 150, 120, 100, 100, 90, 90, 90]
01:24:57.536 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1900] - === 处理表头第一行0宽度设置（特殊合并处理） ===
01:24:57.536 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1901] - 物理单元格数: 8, 逻辑列数: 8
01:24:57.537 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1935] - 普通单元格: 逻辑列0 宽度180
01:24:57.544 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列0 内容='产品名称' 逻辑列0 跨度1 最终宽度: 180像素
01:24:57.544 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1935] - 普通单元格: 逻辑列1 宽度150
01:24:57.545 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列1 内容='规格型号' 逻辑列1 跨度1 最终宽度: 150像素
01:24:57.545 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1935] - 普通单元格: 逻辑列2 宽度120
01:24:57.545 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列2 内容='质量检验' 逻辑列2 跨度1 最终宽度: 120像素
01:24:57.546 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1926] -   + 逻辑列3 宽度: 100像素
01:24:57.546 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1926] -   + 逻辑列4 宽度: 100像素
01:24:57.546 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1931] - 合并单元格总宽度: 200像素
01:24:57.547 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列3 内容='生产信息' 逻辑列3 跨度2 最终宽度: 200像素
01:24:57.547 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1909] - 跳过被合并的单元格: 行0 列4
01:24:57.547 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1926] -   + 逻辑列5 宽度: 90像素
01:24:57.547 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1926] -   + 逻辑列6 宽度: 90像素
01:24:57.548 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1926] -   + 逻辑列7 宽度: 90像素
01:24:57.548 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1931] - 合并单元格总宽度: 270像素
01:24:57.548 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列5 内容='责任人员' 逻辑列5 跨度3 最终宽度: 270像素
01:24:57.548 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1909] - 跳过被合并的单元格: 行0 列6
01:24:57.548 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1909] - 跳过被合并的单元格: 行0 列7
01:24:57.548 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1980] - === 处理表头第二行1宽度设置（特殊对齐处理） ===
01:24:57.549 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1981] - 物理单元格数: 8, 逻辑列数: 8
01:24:57.549 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1992] - 发现第一行合并: 起始列3 跨度2
01:24:57.549 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1992] - 发现第一行合并: 起始列5 跨度3
01:24:57.549 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2050] - 普通单元格: 逻辑列0 宽度180
01:24:57.549 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列0 内容='[空]' 逻辑列0 最终宽度: 180像素
01:24:57.549 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2050] - 普通单元格: 逻辑列1 宽度150
01:24:57.549 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列1 内容='[空]' 逻辑列1 最终宽度: 150像素
01:24:57.550 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2050] - 普通单元格: 逻辑列2 宽度120
01:24:57.550 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列2 内容='[空]' 逻辑列2 最终宽度: 120像素
01:24:57.550 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2039] - 合并单元格下方主单元格: 逻辑列3 相对位置0 总宽度200
01:24:57.551 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列3 内容='批次号' 逻辑列3 最终宽度: 200像素
01:24:57.551 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2089] - 跳过合并单元格范围: 逻辑列3 跳过2列
01:24:57.551 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2039] - 合并单元格下方主单元格: 逻辑列5 相对位置0 总宽度270
01:24:57.551 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列4 内容='日期' 逻辑列5 最终宽度: 270像素
01:24:57.551 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2089] - 跳过合并单元格范围: 逻辑列5 跳过3列
01:24:57.552 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1803] - === 处理第2行宽度设置 ===
01:24:57.553 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1804] - 物理单元格数: 8, 逻辑列数: 8
01:24:57.553 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列0 宽度180像素
01:24:57.553 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列0 内容='智能手机\n（多功能...' 逻辑列0 跨度1 设置宽度: 180像素
01:24:57.553 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列1 宽度150像素
01:24:57.554 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 150像素
01:24:57.554 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列2 宽度120像素
01:24:57.554 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列2 内容='[空]' 逻辑列2 跨度1 设置宽度: 120像素
01:24:57.554 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列3 宽度100像素
01:24:57.554 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列3 内容='[空]' 逻辑列3 跨度1 设置宽度: 100像素
01:24:57.555 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列4 宽度100像素
01:24:57.555 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列4 内容='[空]' 逻辑列4 跨度1 设置宽度: 100像素
01:24:57.555 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列5 宽度90像素
01:24:57.555 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列5 内容='[空]' 逻辑列5 跨度1 设置宽度: 90像素
01:24:57.555 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列6 宽度90像素
01:24:57.555 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列6 内容='[空]' 逻辑列6 跨度1 设置宽度: 90像素
01:24:57.555 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列7 宽度90像素
01:24:57.556 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列7 内容='[空]' 逻辑列7 跨度1 设置宽度: 90像素
01:24:57.556 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1788] - 列宽配置应用完成
01:24:57.556 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2285] - === 表格结构信息 ===
01:24:57.556 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2292] - 第0行，单元格数: 8
01:24:57.556 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列0: 内容='产品名称' 跨度=1 宽度=180像素(2700twips)
01:24:57.557 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列1: 内容='规格型号' 跨度=1 宽度=150像素(2250twips)
01:24:57.557 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列2: 内容='质量检验' 跨度=1 宽度=120像素(1800twips)
01:24:57.557 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列3: 内容='生产信息' 跨度=2 宽度=200像素(3000twips)
01:24:57.557 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列4: 内容='[空]' 跨度=1 宽度=0像素(0twips)
01:24:57.557 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列5: 内容='责任人员' 跨度=3 宽度=270像素(4050twips)
01:24:57.557 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列6: 内容='[空]' 跨度=1 宽度=0像素(0twips)
01:24:57.558 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列7: 内容='[空]' 跨度=1 宽度=0像素(0twips)
01:24:57.558 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2336] - 
01:24:57.558 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2292] - 第1行，单元格数: 8
01:24:57.558 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列0: 内容='[空]' 跨度=1 宽度=180像素(2700twips)
01:24:57.558 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
01:24:57.558 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
01:24:57.558 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列3: 内容='批次号' 跨度=1 宽度=200像素(3000twips)
01:24:57.559 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列4: 内容='日期' 跨度=1 宽度=270像素(4050twips)
01:24:57.559 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列5: 内容='检验员' 跨度=1 宽度=0像素(0twips)
01:24:57.559 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列6: 内容='审核员' 跨度=1 宽度=0像素(0twips)
01:24:57.560 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列7: 内容='负责人' 跨度=1 宽度=0像素(0twips)
01:24:57.560 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2336] - 
01:24:57.560 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2292] - 第2行，单元格数: 8
01:24:57.560 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列0: 内容='智能手机\n（多功能检测）' 跨度=1 宽度=180像素(2700twips)
01:24:57.561 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列1: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
01:24:57.561 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列2: 内容='[空]' 跨度=1 宽度=120像素(1800twips)
01:24:57.561 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列3: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
01:24:57.561 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列4: 内容='[空]' 跨度=1 宽度=100像素(1500twips)
01:24:57.561 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列5: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:24:57.561 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列6: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:24:57.561 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列7: 内容='[空]' 跨度=1 宽度=90像素(1350twips)
01:24:57.561 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2336] - 
01:24:57.562 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [printTableStructure,2339] - === 表格结构信息结束 ===
01:24:57.655 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3079 bytes
01:24:57.674 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_012457.docx, 大小: 3079 bytes
01:27:44.447 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
01:27:44.448 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
01:27:44.448 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 4, 合并单元格数量: 1
01:27:44.448 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
01:27:44.448 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
01:27:44.450 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
01:27:44.451 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 6, 总列数: 8
01:27:44.460 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,680] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi><mo>=</mo><mrow><mi>m</mi><mo>&#x2062;</mo><msup><mi>c</mi><mn>2</mn></msup></mrow></math>}
01:27:44.462 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:27:44.701 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,680] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>&#x3C0;</mi><mo>&#x2062;</mo><msup><mi>r</mi><mn>2</mn></msup></math>}
01:27:44.702 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:27:44.781 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,680] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><msub><mi>x</mi><mi>i</mi></msub></math>}
01:27:44.781 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:27:44.859 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,680] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msubsup><mo>&#x222B;</mo><mn>0</mn><mn>1</mn></msubsup><mi>x</mi><mrow><mi>d</mi><mi>x</mi></mrow></mrow><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math>}
01:27:44.859 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:27:44.941 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,972] - 应用表头合并单元格，数量: 7
01:27:44.941 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 7 个合并单元格，表头行数: 0
01:27:44.941 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,0) -> (1,0) 调整后(0,0) -> (1,0) 内容: 检查项目
01:27:44.942 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,1) -> (1,1) 调整后(0,1) -> (1,1) 内容: 技术要求
01:27:44.942 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,2) -> (1,2) 调整后(0,2) -> (1,2) 内容: 检查结果
01:27:44.943 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,3) -> (0,4) 调整后(0,3) -> (0,4) 内容: 完工
01:27:44.943 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='完工' 起始列3 结束列4 跨度2 实际行0
01:27:44.943 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行0 列4 内容=''
01:27:44.943 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,5) -> (1,5) 调整后(0,5) -> (1,5) 内容: 检查员
01:27:44.943 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,6) -> (1,6) 调整后(0,6) -> (1,6) 内容: 组长
01:27:44.944 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(0,7) -> (1,7) 调整后(0,7) -> (1,7) 内容: 检验员
01:27:44.944 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,980] - 应用数据行合并单元格，数量: 1，表头偏移: 2
01:27:44.944 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1076] - 开始应用 1 个合并单元格，表头行数: 2
01:27:44.944 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1114] - 应用合并单元格: 原始(2,3) -> (3,4) 调整后(4,3) -> (5,4) 内容: 12月17-18日
01:27:44.945 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1162] - 设置横向合并: 内容='12月17-18日' 起始列3 结束列4 跨度2 实际行4
01:27:44.945 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerge,1178] - 设置被合并单元格: 行4 列4 内容='17'
01:27:44.945 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1708] - 应用表头宽度配置: 列宽=[150, 200, 150, 50, 50, 80, 80, 80], 行高=[50, 50]
01:27:44.945 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1718] - 准备应用列宽配置，配置内容: [150, 200, 150, 50, 50, 80, 80, 80]
01:27:44.945 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1764] - 开始应用列宽配置: [150, 200, 150, 50, 50, 80, 80, 80]
01:27:44.945 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1900] - === 处理表头第一行0宽度设置（特殊合并处理） ===
01:27:44.945 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1901] - 物理单元格数: 8, 逻辑列数: 8
01:27:44.946 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1935] - 普通单元格: 逻辑列0 宽度150
01:27:44.946 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列0 内容='检查项目' 逻辑列0 跨度1 最终宽度: 150像素
01:27:44.946 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1935] - 普通单元格: 逻辑列1 宽度200
01:27:44.946 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列1 内容='技术要求' 逻辑列1 跨度1 最终宽度: 200像素
01:27:44.946 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1935] - 普通单元格: 逻辑列2 宽度150
01:27:44.946 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列2 内容='检查结果' 逻辑列2 跨度1 最终宽度: 150像素
01:27:44.947 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1926] -   + 逻辑列3 宽度: 50像素
01:27:44.947 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1926] -   + 逻辑列4 宽度: 50像素
01:27:44.947 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1931] - 合并单元格总宽度: 100像素
01:27:44.947 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列3 内容='完工' 逻辑列3 跨度2 最终宽度: 100像素
01:27:44.947 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1909] - 跳过被合并的单元格: 行0 列4
01:27:44.947 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1935] - 普通单元格: 逻辑列5 宽度80
01:27:44.948 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列5 内容='检查员' 逻辑列5 跨度1 最终宽度: 80像素
01:27:44.948 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1935] - 普通单元格: 逻辑列6 宽度80
01:27:44.948 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列6 内容='组长' 逻辑列6 跨度1 最终宽度: 80像素
01:27:44.948 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1935] - 普通单元格: 逻辑列7 宽度80
01:27:44.949 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderFirstRowWidths,1955] - 行0 列7 内容='检验员' 逻辑列7 跨度1 最终宽度: 80像素
01:27:44.949 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1980] - === 处理表头第二行1宽度设置（特殊对齐处理） ===
01:27:44.949 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1981] - 物理单元格数: 8, 逻辑列数: 8
01:27:44.949 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,1992] - 发现第一行合并: 起始列3 跨度2
01:27:44.949 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2050] - 普通单元格: 逻辑列0 宽度150
01:27:44.949 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列0 内容='[空]' 逻辑列0 最终宽度: 150像素
01:27:44.950 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2050] - 普通单元格: 逻辑列1 宽度200
01:27:44.950 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列1 内容='[空]' 逻辑列1 最终宽度: 200像素
01:27:44.950 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2050] - 普通单元格: 逻辑列2 宽度150
01:27:44.950 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列2 内容='[空]' 逻辑列2 最终宽度: 150像素
01:27:44.950 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2039] - 合并单元格下方主单元格: 逻辑列3 相对位置0 总宽度100
01:27:44.951 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列3 内容='月' 逻辑列3 最终宽度: 100像素
01:27:44.951 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2089] - 跳过合并单元格范围: 逻辑列3 跳过2列
01:27:44.951 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2050] - 普通单元格: 逻辑列5 宽度80
01:27:44.951 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列4 内容='日' 逻辑列5 最终宽度: 80像素
01:27:44.951 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2050] - 普通单元格: 逻辑列6 宽度80
01:27:44.952 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列5 内容='[空]' 逻辑列6 最终宽度: 80像素
01:27:44.952 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2050] - 普通单元格: 逻辑列7 宽度80
01:27:44.952 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderSecondRowWidths,2070] - 行1 列6 内容='[空]' 逻辑列7 最终宽度: 80像素
01:27:44.952 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1803] - === 处理第2行宽度设置 ===
01:27:44.952 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1804] - 物理单元格数: 8, 逻辑列数: 8
01:27:44.953 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列0 宽度150像素
01:27:44.953 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列0 内容='数学公式测试' 逻辑列0 跨度1 设置宽度: 150像素
01:27:44.953 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列1 宽度200像素
01:27:44.953 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 200像素
01:27:44.953 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列2 宽度150像素
01:27:44.954 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列2 内容='合格' 逻辑列2 跨度1 设置宽度: 150像素
01:27:44.954 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列3 宽度50像素
01:27:44.954 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列3 内容='12月17-18日' 逻辑列3 跨度1 设置宽度: 50像素
01:27:44.955 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列4 宽度50像素
01:27:44.955 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列4 内容='15' 逻辑列4 跨度1 设置宽度: 50像素
01:27:44.955 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列5 宽度80像素
01:27:44.956 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列5 内容='张三' 逻辑列5 跨度1 设置宽度: 80像素
01:27:44.956 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列6 宽度80像素
01:27:44.956 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列6 内容='李四' 逻辑列6 跨度1 设置宽度: 80像素
01:27:44.956 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列7 宽度80像素
01:27:44.956 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行2 列7 内容='王五' 逻辑列7 跨度1 设置宽度: 80像素
01:27:44.956 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1803] - === 处理第3行宽度设置 ===
01:27:44.956 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1804] - 物理单元格数: 8, 逻辑列数: 8
01:27:44.956 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列0 宽度150像素
01:27:44.957 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行3 列0 内容='几何计算' 逻辑列0 跨度1 设置宽度: 150像素
01:27:44.957 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列1 宽度200像素
01:27:44.957 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行3 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 200像素
01:27:44.957 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列2 宽度150像素
01:27:44.957 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行3 列2 内容='合格' 逻辑列2 跨度1 设置宽度: 150像素
01:27:44.957 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列3 宽度50像素
01:27:44.957 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行3 列3 内容='12' 逻辑列3 跨度1 设置宽度: 50像素
01:27:44.958 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列4 宽度50像素
01:27:44.958 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行3 列4 内容='16' 逻辑列4 跨度1 设置宽度: 50像素
01:27:44.958 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列5 宽度80像素
01:27:44.958 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行3 列5 内容='张三' 逻辑列5 跨度1 设置宽度: 80像素
01:27:44.958 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列6 宽度80像素
01:27:44.958 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行3 列6 内容='李四' 逻辑列6 跨度1 设置宽度: 80像素
01:27:44.958 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列7 宽度80像素
01:27:44.959 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行3 列7 内容='王五' 逻辑列7 跨度1 设置宽度: 80像素
01:27:44.959 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1803] - === 处理第4行宽度设置 ===
01:27:44.959 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1804] - 物理单元格数: 8, 逻辑列数: 8
01:27:44.959 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列0 宽度150像素
01:27:44.959 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行4 列0 内容='统计分析' 逻辑列0 跨度1 设置宽度: 150像素
01:27:44.959 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列1 宽度200像素
01:27:44.959 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行4 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 200像素
01:27:44.959 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列2 宽度150像素
01:27:44.960 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行4 列2 内容='合格' 逻辑列2 跨度1 设置宽度: 150像素
01:27:44.960 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2168] - 计算合并单元格宽度: 起始列3 结束列4 跨度2
01:27:44.960 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2174] -   + 逻辑列3 宽度: 50像素
01:27:44.960 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2174] -   + 逻辑列4 宽度: 50像素
01:27:44.960 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2180] - 合并单元格总宽度: 100像素
01:27:44.960 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行4 列3 内容='12月17-18日' 逻辑列3 跨度2 设置宽度: 100像素
01:27:44.960 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1814] - 跳过被合并的单元格: 行4 列4
01:27:44.961 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列5 宽度80像素
01:27:44.961 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行4 列5 内容='张三' 逻辑列5 跨度1 设置宽度: 80像素
01:27:44.961 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列6 宽度80像素
01:27:44.961 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行4 列6 内容='李四' 逻辑列6 跨度1 设置宽度: 80像素
01:27:44.961 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列7 宽度80像素
01:27:44.962 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行4 列7 内容='王五' 逻辑列7 跨度1 设置宽度: 80像素
01:27:44.962 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1803] - === 处理第5行宽度设置 ===
01:27:44.962 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1804] - 物理单元格数: 8, 逻辑列数: 8
01:27:44.962 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列0 宽度150像素
01:27:44.962 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行5 列0 内容='积分计算' 逻辑列0 跨度1 设置宽度: 150像素
01:27:44.962 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列1 宽度200像素
01:27:44.962 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行5 列1 内容='[空]' 逻辑列1 跨度1 设置宽度: 200像素
01:27:44.963 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列2 宽度150像素
01:27:44.963 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行5 列2 内容='合格' 逻辑列2 跨度1 设置宽度: 150像素
01:27:44.963 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1814] - 跳过被合并的单元格: 行5 列3
01:27:44.963 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1814] - 跳过被合并的单元格: 行5 列4
01:27:44.963 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列3 宽度50像素
01:27:44.963 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行5 列5 内容='张三' 逻辑列3 跨度1 设置宽度: 50像素
01:27:44.963 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列4 宽度50像素
01:27:44.964 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行5 列6 内容='李四' 逻辑列4 跨度1 设置宽度: 50像素
01:27:44.964 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [calculateCellWidthForRow,2186] - 非合并单元格宽度: 逻辑列5 宽度80像素
01:27:44.964 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyRowColumnWidths,1835] - 行5 列7 内容='王五' 逻辑列5 跨度1 设置宽度: 80像素
01:27:44.964 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1788] - 列宽配置应用完成
01:27:44.964 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2285] - === 表格结构信息 ===
01:27:44.964 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2292] - 第0行，单元格数: 8
01:27:44.965 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列0: 内容='检查项目' 跨度=1 宽度=150像素(2250twips)
01:27:44.965 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列1: 内容='技术要求' 跨度=1 宽度=200像素(3000twips)
01:27:44.965 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列2: 内容='检查结果' 跨度=1 宽度=150像素(2250twips)
01:27:44.965 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列3: 内容='完工' 跨度=2 宽度=100像素(1500twips)
01:27:44.965 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列4: 内容='[空]' 跨度=1 宽度=0像素(0twips)
01:27:44.966 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列5: 内容='检查员' 跨度=1 宽度=80像素(1200twips)
01:27:44.966 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列6: 内容='组长' 跨度=1 宽度=80像素(1200twips)
01:27:44.966 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列7: 内容='检验员' 跨度=1 宽度=80像素(1200twips)
01:27:44.966 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2336] - 
01:27:44.966 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2292] - 第1行，单元格数: 8
01:27:44.966 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列0: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
01:27:44.966 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列1: 内容='[空]' 跨度=1 宽度=200像素(3000twips)
01:27:44.967 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列2: 内容='[空]' 跨度=1 宽度=150像素(2250twips)
01:27:44.967 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列3: 内容='月' 跨度=1 宽度=100像素(1500twips)
01:27:44.967 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列4: 内容='日' 跨度=1 宽度=80像素(1200twips)
01:27:44.967 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列5: 内容='[空]' 跨度=1 宽度=80像素(1200twips)
01:27:44.967 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列6: 内容='[空]' 跨度=1 宽度=80像素(1200twips)
01:27:44.967 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列7: 内容='[空]' 跨度=1 宽度=0像素(0twips)
01:27:44.967 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2336] - 
01:27:44.967 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2292] - 第2行，单元格数: 8
01:27:44.967 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列0: 内容='数学公式测试' 跨度=1 宽度=150像素(2250twips)
01:27:44.968 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列1: 内容='[空]' 跨度=1 宽度=200像素(3000twips)
01:27:44.968 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列2: 内容='合格' 跨度=1 宽度=150像素(2250twips)
01:27:44.968 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列3: 内容='12月17-18日' 跨度=1 宽度=50像素(750twips)
01:27:44.968 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列4: 内容='15' 跨度=1 宽度=50像素(750twips)
01:27:44.968 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列5: 内容='张三' 跨度=1 宽度=80像素(1200twips)
01:27:44.969 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列6: 内容='李四' 跨度=1 宽度=80像素(1200twips)
01:27:44.969 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列7: 内容='王五' 跨度=1 宽度=80像素(1200twips)
01:27:44.969 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2336] - 
01:27:44.969 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2292] - 第3行，单元格数: 8
01:27:44.969 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列0: 内容='几何计算' 跨度=1 宽度=150像素(2250twips)
01:27:44.969 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列1: 内容='[空]' 跨度=1 宽度=200像素(3000twips)
01:27:44.969 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列2: 内容='合格' 跨度=1 宽度=150像素(2250twips)
01:27:44.969 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列3: 内容='12' 跨度=1 宽度=50像素(750twips)
01:27:44.970 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列4: 内容='16' 跨度=1 宽度=50像素(750twips)
01:27:44.970 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列5: 内容='张三' 跨度=1 宽度=80像素(1200twips)
01:27:44.970 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列6: 内容='李四' 跨度=1 宽度=80像素(1200twips)
01:27:44.970 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列7: 内容='王五' 跨度=1 宽度=80像素(1200twips)
01:27:44.970 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2336] - 
01:27:44.970 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2292] - 第4行，单元格数: 8
01:27:44.970 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列0: 内容='统计分析' 跨度=1 宽度=150像素(2250twips)
01:27:44.970 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列1: 内容='[空]' 跨度=1 宽度=200像素(3000twips)
01:27:44.971 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列2: 内容='合格' 跨度=1 宽度=150像素(2250twips)
01:27:44.971 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列3: 内容='12月17-18日' 跨度=2 宽度=100像素(1500twips)
01:27:44.971 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列4: 内容='17' 跨度=1 宽度=0像素(0twips)
01:27:44.971 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列5: 内容='张三' 跨度=1 宽度=80像素(1200twips)
01:27:44.971 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列6: 内容='李四' 跨度=1 宽度=80像素(1200twips)
01:27:44.971 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列7: 内容='王五' 跨度=1 宽度=80像素(1200twips)
01:27:44.971 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2336] - 
01:27:44.971 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2292] - 第5行，单元格数: 8
01:27:44.971 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列0: 内容='积分计算' 跨度=1 宽度=150像素(2250twips)
01:27:44.972 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列1: 内容='[空]' 跨度=1 宽度=200像素(3000twips)
01:27:44.972 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列2: 内容='合格' 跨度=1 宽度=150像素(2250twips)
01:27:44.972 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列3: 内容='12' 跨度=1 宽度=0像素(0twips)
01:27:44.972 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列4: 内容='18' 跨度=1 宽度=0像素(0twips)
01:27:44.972 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列5: 内容='张三' 跨度=1 宽度=50像素(750twips)
01:27:44.972 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列6: 内容='李四' 跨度=1 宽度=50像素(750twips)
01:27:44.972 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2332] -   列7: 内容='王五' 跨度=1 宽度=80像素(1200twips)
01:27:44.972 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2336] - 
01:27:44.972 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [printTableStructure,2339] - === 表格结构信息结束 ===
01:27:44.979 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3612 bytes
01:27:44.983 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_012744.docx, 大小: 3612 bytes
01:29:30.235 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
01:29:30.238 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
01:29:34.684 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1047153 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
01:29:34.688 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
01:29:35.540 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
01:29:35.541 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
01:29:35.541 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
01:29:35.583 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
01:29:36.101 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
01:29:36.325 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
01:29:36.963 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
01:29:36.977 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.687 seconds (JVM running for 3.068)
01:29:40.566 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:29:40.656 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
01:29:40.657 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
01:29:40.658 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 4, 合并单元格数量: 1
01:29:40.658 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
01:29:40.659 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
01:29:40.962 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
01:29:41.001 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,939] - 创建表格，总行数: 6, 总列数: 8
01:29:41.074 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi><mo>=</mo><mrow><mi>m</mi><mo>&#x2062;</mo><msup><mi>c</mi><mn>2</mn></msup></mrow></math>}
01:29:41.076 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:29:41.335 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>&#x3C0;</mi><mo>&#x2062;</mo><msup><mi>r</mi><mn>2</mn></msup></math>}
01:29:41.336 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:29:41.415 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><msub><mi>x</mi><mi>i</mi></msub></math>}
01:29:41.416 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:29:41.517 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msubsup><mo>&#x222B;</mo><mn>0</mn><mn>1</mn></msubsup><mi>x</mi><mrow><mi>d</mi><mi>x</mi></mrow></mrow><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math>}
01:29:41.517 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:29:41.621 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,970] - 应用表头合并单元格，数量: 7
01:29:41.621 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 7 个合并单元格，表头行数: 0
01:29:41.625 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,978] - 应用数据行合并单元格，数量: 1，表头偏移: 2
01:29:41.626 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 1 个合并单元格，表头行数: 2
01:29:41.690 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3547 bytes
01:29:41.703 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_012941.docx, 大小: 3547 bytes
01:30:01.502 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
01:30:01.503 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
01:30:01.503 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
01:30:01.503 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
01:30:01.503 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
01:30:01.505 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
01:30:01.505 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,939] - 创建表格，总行数: 5, 总列数: 8
01:30:01.523 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
01:30:01.523 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
01:30:01.591 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,970] - 应用表头合并单元格，数量: 5
01:30:01.592 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 5 个合并单元格，表头行数: 0
01:30:01.592 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,978] - 应用数据行合并单元格，数量: 1，表头偏移: 2
01:30:01.593 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 1 个合并单元格，表头行数: 2
01:30:01.599 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3425 bytes
01:30:01.603 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_013001.docx, 大小: 3425 bytes
10:56:12.022 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:56:12.030 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:56:21.630 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1250267 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
10:56:21.634 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:56:24.936 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
10:56:24.937 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:56:24.937 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:56:24.999 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:56:26.394 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:56:27.005 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:56:28.397 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
10:56:28.426 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 5.316 seconds (JVM running for 5.86)
10:56:39.786 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:56:39.892 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
10:56:39.893 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
10:56:39.894 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
10:56:39.895 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithJson,880] - 开始导出Word文档，表格标题: 检验记录表
10:56:39.895 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithJson,881] - 合并单元格数量: 1
10:56:40.206 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,811] - 已设置文档为横向纸张
10:56:40.251 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,936] - 创建表格，总行数: 3, 总列数: 6
10:56:40.348 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,967] - 应用表头合并单元格，数量: 3
10:56:40.349 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1095] - 开始应用 3 个合并单元格，表头行数: 0
10:56:40.356 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,975] - 应用数据行合并单元格，数量: 1，表头偏移: 2
10:56:40.357 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1095] - 开始应用 1 个合并单元格，表头行数: 2
10:56:40.456 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithJson,909] - 包含合并单元格的Word文档导出完成，文件大小: 3000 bytes
10:56:40.474 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_105640.docx, 大小: 3000 bytes
10:57:58.409 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
10:57:58.409 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
10:57:58.410 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
10:57:58.410 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithJson,880] - 开始导出Word文档，表格标题: 检验记录表
10:57:58.410 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithJson,881] - 合并单元格数量: 1
10:57:58.412 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,811] - 已设置文档为横向纸张
10:57:58.413 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,936] - 创建表格，总行数: 3, 总列数: 6
10:57:58.424 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,967] - 应用表头合并单元格，数量: 3
10:57:58.425 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1095] - 开始应用 3 个合并单元格，表头行数: 0
10:57:58.425 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,975] - 应用数据行合并单元格，数量: 1，表头偏移: 2
10:57:58.425 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1095] - 开始应用 1 个合并单元格，表头行数: 2
10:57:58.433 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithJson,909] - 包含合并单元格的Word文档导出完成，文件大小: 3001 bytes
10:57:58.438 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_105758.docx, 大小: 3001 bytes
10:59:59.947 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
10:59:59.947 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
10:59:59.948 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 1, 合并单元格数量: 1
10:59:59.948 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithJson,880] - 开始导出Word文档，表格标题: 检验记录表
10:59:59.948 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithJson,881] - 合并单元格数量: 1
10:59:59.949 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,811] - 已设置文档为横向纸张
10:59:59.949 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,936] - 创建表格，总行数: 3, 总列数: 6
10:59:59.960 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,967] - 应用表头合并单元格，数量: 3
10:59:59.961 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerges,1095] - 开始应用 3 个合并单元格，表头行数: 0
10:59:59.961 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,975] - 应用数据行合并单元格，数量: 1，表头偏移: 2
10:59:59.961 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerges,1095] - 开始应用 1 个合并单元格，表头行数: 2
10:59:59.966 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithJson,909] - 包含合并单元格的Word文档导出完成，文件大小: 2957 bytes
10:59:59.969 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250821_105959.docx, 大小: 2957 bytes
