package com.logictrue.word.service;

import com.logictrue.word.dto.TableExportRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Word导出服务合并单元格宽度高度测试
 */
@SpringBootTest
public class WordExportServiceMergeTest {

    @Autowired
    private WordExportService wordExportService;

    @Test
    public void testExportWithMergeCellSizes() throws IOException {
        // 创建测试请求
        TableExportRequest request = createTestRequestWithMergeSizes();

        // 导出Word文档
        byte[] wordBytes = wordExportService.exportTableToWordWithJson(request);

        // 保存到文件进行验证
        try (FileOutputStream fos = new FileOutputStream("test_merge_sizes.docx")) {
            fos.write(wordBytes);
        }

        System.out.println("测试文档已生成: test_merge_sizes.docx");
        System.out.println("文档大小: " + wordBytes.length + " bytes");

        // 验证headerWidthConfig是否正确应用
        TableExportRequest.HeaderWidthConfig config = request.getHeaderWidthConfig();
        System.out.println("HeaderWidthConfig列宽: " + config.getColumnWidths());
        System.out.println("HeaderWidthConfig行高: " + config.getHeaderHeights());
    }

    @Test
    public void testHeaderWidthConfigApplication() throws IOException {
        // 创建专门测试headerWidthConfig的请求
        TableExportRequest request = createHeaderWidthConfigTestRequest();

        // 导出Word文档
        byte[] wordBytes = wordExportService.exportTableToWordWithJson(request);

        // 保存到文件进行验证
        try (FileOutputStream fos = new FileOutputStream("test_header_width_config.docx")) {
            fos.write(wordBytes);
        }

        System.out.println("HeaderWidthConfig测试文档已生成: test_header_width_config.docx");
        System.out.println("文档大小: " + wordBytes.length + " bytes");
    }

    /**
     * 创建专门测试headerWidthConfig的请求
     */
    private TableExportRequest createHeaderWidthConfigTestRequest() {
        TableExportRequest request = new TableExportRequest();
        request.setTitle("HeaderWidthConfig测试表");
        request.setPageOrientation("LANDSCAPE");

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();

        // 创建简单的表头数据
        List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();

        // 第一行表头
        List<TableExportRequest.HeaderCell> headerRow1 = new ArrayList<>();
        headerRow1.add(createHeaderCell("列1", null, null));
        headerRow1.add(createHeaderCell("列2", null, null));
        headerRow1.add(createHeaderCell("列3", null, null));
        headerRow1.add(createHeaderCell("列4", null, null));
        headerRow1.add(createHeaderCell("列5", null, null));
        headerRow1.add(createHeaderCell("列6", null, null));
        headers.add(headerRow1);

        tableData.setHeaders(headers);

        // 创建数据行
        List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();
        List<TableExportRequest.DataCell> dataRow1 = new ArrayList<>();
        dataRow1.add(createDataCell("数据1", null, null));
        dataRow1.add(createDataCell("数据2", null, null));
        dataRow1.add(createDataCell("数据3", null, null));
        dataRow1.add(createDataCell("数据4", null, null));
        dataRow1.add(createDataCell("数据5", null, null));
        dataRow1.add(createDataCell("数据6", null, null));
        dataRows.add(dataRow1);

        tableData.setDataRows(dataRows);
        request.setTableData(tableData);

        // 设置表头宽度配置 - 这是测试的重点
        TableExportRequest.HeaderWidthConfig headerWidthConfig = new TableExportRequest.HeaderWidthConfig();
        List<Integer> columnWidths = new ArrayList<>();
        columnWidths.add(200);  // 第1列：200px
        columnWidths.add(150);  // 第2列：150px
        columnWidths.add(100);  // 第3列：100px
        columnWidths.add(120);  // 第4列：120px
        columnWidths.add(80);   // 第5列：80px
        columnWidths.add(90);   // 第6列：90px
        headerWidthConfig.setColumnWidths(columnWidths);

        List<Integer> headerHeights = new ArrayList<>();
        headerHeights.add(50);  // 表头行高：50px
        headerWidthConfig.setHeaderHeights(headerHeights);

        request.setHeaderWidthConfig(headerWidthConfig);

        System.out.println("创建HeaderWidthConfig测试请求:");
        System.out.println("列宽配置: " + columnWidths);
        System.out.println("行高配置: " + headerHeights);

        return request;
    }

    @Test
    public void testHeaderWidthConfigWithRealData() throws IOException {
        // 使用真实的JSON数据格式进行测试
        TableExportRequest request = createRealDataTestRequest();

        // 导出Word文档
        byte[] wordBytes = wordExportService.exportTableToWordWithJson(request);

        // 保存到文件进行验证
        try (FileOutputStream fos = new FileOutputStream("test_real_data_with_config.docx")) {
            fos.write(wordBytes);
        }

        System.out.println("真实数据测试文档已生成: test_real_data_with_config.docx");
        System.out.println("文档大小: " + wordBytes.length + " bytes");
    }

    /**
     * 创建使用真实JSON数据格式的测试请求
     */
    private TableExportRequest createRealDataTestRequest() {
        TableExportRequest request = new TableExportRequest();
        request.setTitle("智能设备检验记录表");
        request.setPageOrientation("LANDSCAPE");

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();

        // 创建表头数据（模拟真实的JSON数据）
        List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();

        // 第一行表头
        List<TableExportRequest.HeaderCell> headerRow1 = new ArrayList<>();
        headerRow1.add(createHeaderCell("产品名称", null, null));
        headerRow1.add(createHeaderCell("生产信息", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headerRow1.add(createHeaderCell("责任人员", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headers.add(headerRow1);

        // 第二行表头
        List<TableExportRequest.HeaderCell> headerRow2 = new ArrayList<>();
        headerRow2.add(createHeaderCell("", null, null));
        headerRow2.add(createHeaderCell("批次号", null, null));
        headerRow2.add(createHeaderCell("日期", null, null));
        headerRow2.add(createHeaderCell("检验员", null, null));
        headerRow2.add(createHeaderCell("审核员", null, null));
        headerRow2.add(createHeaderCell("负责人", null, null));
        headers.add(headerRow2);

        tableData.setHeaders(headers);

        // 创建数据行
        List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();
        List<TableExportRequest.DataCell> dataRow1 = new ArrayList<>();
        dataRow1.add(createDataCell("智能手机\n（多功能检测）", null, null));
        dataRow1.add(createDataCell("A001", null, null));
        dataRow1.add(createDataCell("2024-01-15", null, null));
        dataRow1.add(createDataCell("张三", null, null));
        dataRow1.add(createDataCell("李四", null, null));
        dataRow1.add(createDataCell("王五", null, null));
        dataRows.add(dataRow1);

        tableData.setDataRows(dataRows);
        request.setTableData(tableData);

        // 设置表头宽度配置（关键测试点）
        TableExportRequest.HeaderWidthConfig headerWidthConfig = new TableExportRequest.HeaderWidthConfig();
        List<Integer> columnWidths = new ArrayList<>();
        columnWidths.add(180);  // 产品名称列
        columnWidths.add(100);  // 批次号列
        columnWidths.add(100);  // 日期列
        columnWidths.add(80);   // 检验员列
        columnWidths.add(80);   // 审核员列
        columnWidths.add(80);   // 负责人列
        headerWidthConfig.setColumnWidths(columnWidths);

        List<Integer> headerHeights = new ArrayList<>();
        headerHeights.add(60);  // 第一行表头高度
        headerHeights.add(40);  // 第二行表头高度
        headerWidthConfig.setHeaderHeights(headerHeights);

        request.setHeaderWidthConfig(headerWidthConfig);

        // 创建表头合并信息（根据headerWidthConfig计算宽度和高度）
        List<TableExportRequest.MergeCell> headerMerges = new ArrayList<>();

        // 产品名称 - 跨2行1列，宽度=180，高度=60+40=100
        TableExportRequest.MergeCell headerMerge1 = new TableExportRequest.MergeCell();
        headerMerge1.setStartRow(0);
        headerMerge1.setStartCol(0);
        headerMerge1.setEndRow(1);
        headerMerge1.setEndCol(0);
        headerMerge1.setRowspan(2);
        headerMerge1.setColspan(1);
        headerMerge1.setWidth(180);  // 应该根据headerWidthConfig计算
        headerMerge1.setHeight(100); // 应该根据headerWidthConfig计算
        headerMerge1.setContent("产品名称");
        headerMerges.add(headerMerge1);

        // 生产信息 - 跨1行2列，宽度=100+100=200，高度=60
        TableExportRequest.MergeCell headerMerge2 = new TableExportRequest.MergeCell();
        headerMerge2.setStartRow(0);
        headerMerge2.setStartCol(1);
        headerMerge2.setEndRow(0);
        headerMerge2.setEndCol(2);
        headerMerge2.setRowspan(1);
        headerMerge2.setColspan(2);
        headerMerge2.setWidth(200);  // 应该根据headerWidthConfig计算
        headerMerge2.setHeight(60);  // 应该根据headerWidthConfig计算
        headerMerge2.setContent("生产信息");
        headerMerges.add(headerMerge2);

        // 责任人员 - 跨1行3列，宽度=80+80+80=240，高度=60
        TableExportRequest.MergeCell headerMerge3 = new TableExportRequest.MergeCell();
        headerMerge3.setStartRow(0);
        headerMerge3.setStartCol(3);
        headerMerge3.setEndRow(0);
        headerMerge3.setEndCol(5);
        headerMerge3.setRowspan(1);
        headerMerge3.setColspan(3);
        headerMerge3.setWidth(240);  // 应该根据headerWidthConfig计算
        headerMerge3.setHeight(60);  // 应该根据headerWidthConfig计算
        headerMerge3.setContent("责任人员");
        headerMerges.add(headerMerge3);

        request.setHeaderMerges(headerMerges);

        // 创建数据行合并信息
        List<TableExportRequest.MergeCell> dataMerges = new ArrayList<>();

        // 产品名称数据 - 跨2行1列，宽度=180，高度=100
        TableExportRequest.MergeCell dataMerge1 = new TableExportRequest.MergeCell();
        dataMerge1.setStartRow(0);
        dataMerge1.setStartCol(0);
        dataMerge1.setEndRow(1);
        dataMerge1.setEndCol(0);
        dataMerge1.setRowspan(2);
        dataMerge1.setColspan(1);
        dataMerge1.setWidth(180);  // 应该根据headerWidthConfig计算
        dataMerge1.setHeight(100);
        dataMerge1.setContent("智能手机\n（多功能检测）");
        dataMerges.add(dataMerge1);

        request.setMerges(dataMerges);

        System.out.println("创建真实数据测试请求:");
        System.out.println("HeaderWidthConfig列宽: " + columnWidths);
        System.out.println("HeaderWidthConfig行高: " + headerHeights);

        return request;
    }

    /**
     * 创建包含合并单元格宽度高度信息的测试请求
     */
    private TableExportRequest createTestRequestWithMergeSizes() {
        TableExportRequest request = new TableExportRequest();
        request.setTitle("合并单元格宽度高度测试表");
        request.setPageOrientation("LANDSCAPE");

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();
        
        // 创建表头数据
        List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();
        
        // 第一行表头
        List<TableExportRequest.HeaderCell> headerRow1 = new ArrayList<>();
        headerRow1.add(createHeaderCell("产品名称", null, null));
        headerRow1.add(createHeaderCell("生产信息", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headerRow1.add(createHeaderCell("责任人员", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headers.add(headerRow1);
        
        // 第二行表头
        List<TableExportRequest.HeaderCell> headerRow2 = new ArrayList<>();
        headerRow2.add(createHeaderCell("", null, null));
        headerRow2.add(createHeaderCell("批次号", null, null));
        headerRow2.add(createHeaderCell("日期", null, null));
        headerRow2.add(createHeaderCell("检验员", null, null));
        headerRow2.add(createHeaderCell("审核员", null, null));
        headerRow2.add(createHeaderCell("负责人", null, null));
        headers.add(headerRow2);
        
        tableData.setHeaders(headers);

        // 创建数据行
        List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();
        List<TableExportRequest.DataCell> dataRow1 = new ArrayList<>();
        dataRow1.add(createDataCell("智能手机\n（多功能检测）", null, null));
        dataRow1.add(createDataCell("A001", null, null));
        dataRow1.add(createDataCell("2024-01-15", null, null));
        dataRow1.add(createDataCell("张三", null, null));
        dataRow1.add(createDataCell("李四", null, null));
        dataRow1.add(createDataCell("王五", null, null));
        dataRows.add(dataRow1);
        
        tableData.setDataRows(dataRows);
        request.setTableData(tableData);

        // 创建表头合并信息（包含宽度和高度）
        List<TableExportRequest.MergeCell> headerMerges = new ArrayList<>();
        
        // 产品名称 - 跨2行1列
        TableExportRequest.MergeCell headerMerge1 = new TableExportRequest.MergeCell();
        headerMerge1.setStartRow(0);
        headerMerge1.setStartCol(0);
        headerMerge1.setEndRow(1);
        headerMerge1.setEndCol(0);
        headerMerge1.setRowspan(2);
        headerMerge1.setColspan(1);
        headerMerge1.setWidth(180);  // 新增：宽度信息
        headerMerge1.setHeight(100); // 新增：高度信息
        headerMerge1.setContent("产品名称");
        headerMerges.add(headerMerge1);
        
        // 生产信息 - 跨1行2列
        TableExportRequest.MergeCell headerMerge2 = new TableExportRequest.MergeCell();
        headerMerge2.setStartRow(0);
        headerMerge2.setStartCol(1);
        headerMerge2.setEndRow(0);
        headerMerge2.setEndCol(2);
        headerMerge2.setRowspan(1);
        headerMerge2.setColspan(2);
        headerMerge2.setWidth(200);  // 新增：宽度信息
        headerMerge2.setHeight(60);  // 新增：高度信息
        headerMerge2.setContent("生产信息");
        headerMerges.add(headerMerge2);
        
        // 责任人员 - 跨1行3列
        TableExportRequest.MergeCell headerMerge3 = new TableExportRequest.MergeCell();
        headerMerge3.setStartRow(0);
        headerMerge3.setStartCol(3);
        headerMerge3.setEndRow(0);
        headerMerge3.setEndCol(5);
        headerMerge3.setRowspan(1);
        headerMerge3.setColspan(3);
        headerMerge3.setWidth(240);  // 新增：宽度信息
        headerMerge3.setHeight(60);  // 新增：高度信息
        headerMerge3.setContent("责任人员");
        headerMerges.add(headerMerge3);
        
        request.setHeaderMerges(headerMerges);

        // 创建数据行合并信息（包含宽度和高度）
        List<TableExportRequest.MergeCell> dataMerges = new ArrayList<>();
        
        // 产品名称数据 - 跨2行1列
        TableExportRequest.MergeCell dataMerge1 = new TableExportRequest.MergeCell();
        dataMerge1.setStartRow(0);
        dataMerge1.setStartCol(0);
        dataMerge1.setEndRow(1);
        dataMerge1.setEndCol(0);
        dataMerge1.setRowspan(2);
        dataMerge1.setColspan(1);
        dataMerge1.setWidth(180);  // 新增：宽度信息
        dataMerge1.setHeight(100); // 新增：高度信息
        dataMerge1.setContent("智能手机\n（多功能检测）");
        dataMerges.add(dataMerge1);
        
        request.setMerges(dataMerges);

        // 设置表头宽度配置
        TableExportRequest.HeaderWidthConfig headerWidthConfig = new TableExportRequest.HeaderWidthConfig();
        List<Integer> columnWidths = new ArrayList<>();
        columnWidths.add(180);
        columnWidths.add(100);
        columnWidths.add(100);
        columnWidths.add(80);
        columnWidths.add(80);
        columnWidths.add(80);
        headerWidthConfig.setColumnWidths(columnWidths);
        
        List<Integer> headerHeights = new ArrayList<>();
        headerHeights.add(60);
        headerHeights.add(40);
        headerWidthConfig.setHeaderHeights(headerHeights);
        
        request.setHeaderWidthConfig(headerWidthConfig);

        return request;
    }

    private TableExportRequest.HeaderCell createHeaderCell(String content, Integer width, Integer height) {
        TableExportRequest.HeaderCell cell = new TableExportRequest.HeaderCell();
        cell.setContent(content);
        cell.setWidth(width);
        cell.setHeight(height);
        return cell;
    }

    private TableExportRequest.DataCell createDataCell(String content, Integer width, Integer height) {
        TableExportRequest.DataCell cell = new TableExportRequest.DataCell();
        cell.setContent(content);
        cell.setWidth(width);
        cell.setHeight(height);
        cell.setHasMath(false);
        return cell;
    }
}
