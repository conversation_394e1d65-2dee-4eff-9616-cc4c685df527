package com.logictrue.word.service;

import com.logictrue.word.dto.TableExportRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Word导出服务合并单元格宽度高度测试
 */
@SpringBootTest
public class WordExportServiceMergeTest {

    @Autowired
    private WordExportService wordExportService;

    @Test
    public void testExportWithMergeCellSizes() throws IOException {
        // 创建测试请求
        TableExportRequest request = createTestRequestWithMergeSizes();
        
        // 导出Word文档
        byte[] wordBytes = wordExportService.exportTableToWordWithJson(request);
        
        // 保存到文件进行验证
        try (FileOutputStream fos = new FileOutputStream("test_merge_sizes.docx")) {
            fos.write(wordBytes);
        }
        
        System.out.println("测试文档已生成: test_merge_sizes.docx");
        System.out.println("文档大小: " + wordBytes.length + " bytes");
    }

    /**
     * 创建包含合并单元格宽度高度信息的测试请求
     */
    private TableExportRequest createTestRequestWithMergeSizes() {
        TableExportRequest request = new TableExportRequest();
        request.setTitle("合并单元格宽度高度测试表");
        request.setPageOrientation("LANDSCAPE");

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();
        
        // 创建表头数据
        List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();
        
        // 第一行表头
        List<TableExportRequest.HeaderCell> headerRow1 = new ArrayList<>();
        headerRow1.add(createHeaderCell("产品名称", null, null));
        headerRow1.add(createHeaderCell("生产信息", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headerRow1.add(createHeaderCell("责任人员", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headers.add(headerRow1);
        
        // 第二行表头
        List<TableExportRequest.HeaderCell> headerRow2 = new ArrayList<>();
        headerRow2.add(createHeaderCell("", null, null));
        headerRow2.add(createHeaderCell("批次号", null, null));
        headerRow2.add(createHeaderCell("日期", null, null));
        headerRow2.add(createHeaderCell("检验员", null, null));
        headerRow2.add(createHeaderCell("审核员", null, null));
        headerRow2.add(createHeaderCell("负责人", null, null));
        headers.add(headerRow2);
        
        tableData.setHeaders(headers);

        // 创建数据行
        List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();
        List<TableExportRequest.DataCell> dataRow1 = new ArrayList<>();
        dataRow1.add(createDataCell("智能手机\n（多功能检测）", null, null));
        dataRow1.add(createDataCell("A001", null, null));
        dataRow1.add(createDataCell("2024-01-15", null, null));
        dataRow1.add(createDataCell("张三", null, null));
        dataRow1.add(createDataCell("李四", null, null));
        dataRow1.add(createDataCell("王五", null, null));
        dataRows.add(dataRow1);
        
        tableData.setDataRows(dataRows);
        request.setTableData(tableData);

        // 创建表头合并信息（包含宽度和高度）
        List<TableExportRequest.MergeCell> headerMerges = new ArrayList<>();
        
        // 产品名称 - 跨2行1列
        TableExportRequest.MergeCell headerMerge1 = new TableExportRequest.MergeCell();
        headerMerge1.setStartRow(0);
        headerMerge1.setStartCol(0);
        headerMerge1.setEndRow(1);
        headerMerge1.setEndCol(0);
        headerMerge1.setRowspan(2);
        headerMerge1.setColspan(1);
        headerMerge1.setWidth(180);  // 新增：宽度信息
        headerMerge1.setHeight(100); // 新增：高度信息
        headerMerge1.setContent("产品名称");
        headerMerges.add(headerMerge1);
        
        // 生产信息 - 跨1行2列
        TableExportRequest.MergeCell headerMerge2 = new TableExportRequest.MergeCell();
        headerMerge2.setStartRow(0);
        headerMerge2.setStartCol(1);
        headerMerge2.setEndRow(0);
        headerMerge2.setEndCol(2);
        headerMerge2.setRowspan(1);
        headerMerge2.setColspan(2);
        headerMerge2.setWidth(200);  // 新增：宽度信息
        headerMerge2.setHeight(60);  // 新增：高度信息
        headerMerge2.setContent("生产信息");
        headerMerges.add(headerMerge2);
        
        // 责任人员 - 跨1行3列
        TableExportRequest.MergeCell headerMerge3 = new TableExportRequest.MergeCell();
        headerMerge3.setStartRow(0);
        headerMerge3.setStartCol(3);
        headerMerge3.setEndRow(0);
        headerMerge3.setEndCol(5);
        headerMerge3.setRowspan(1);
        headerMerge3.setColspan(3);
        headerMerge3.setWidth(240);  // 新增：宽度信息
        headerMerge3.setHeight(60);  // 新增：高度信息
        headerMerge3.setContent("责任人员");
        headerMerges.add(headerMerge3);
        
        request.setHeaderMerges(headerMerges);

        // 创建数据行合并信息（包含宽度和高度）
        List<TableExportRequest.MergeCell> dataMerges = new ArrayList<>();
        
        // 产品名称数据 - 跨2行1列
        TableExportRequest.MergeCell dataMerge1 = new TableExportRequest.MergeCell();
        dataMerge1.setStartRow(0);
        dataMerge1.setStartCol(0);
        dataMerge1.setEndRow(1);
        dataMerge1.setEndCol(0);
        dataMerge1.setRowspan(2);
        dataMerge1.setColspan(1);
        dataMerge1.setWidth(180);  // 新增：宽度信息
        dataMerge1.setHeight(100); // 新增：高度信息
        dataMerge1.setContent("智能手机\n（多功能检测）");
        dataMerges.add(dataMerge1);
        
        request.setMerges(dataMerges);

        // 设置表头宽度配置
        TableExportRequest.HeaderWidthConfig headerWidthConfig = new TableExportRequest.HeaderWidthConfig();
        List<Integer> columnWidths = new ArrayList<>();
        columnWidths.add(180);
        columnWidths.add(100);
        columnWidths.add(100);
        columnWidths.add(80);
        columnWidths.add(80);
        columnWidths.add(80);
        headerWidthConfig.setColumnWidths(columnWidths);
        
        List<Integer> headerHeights = new ArrayList<>();
        headerHeights.add(60);
        headerHeights.add(40);
        headerWidthConfig.setHeaderHeights(headerHeights);
        
        request.setHeaderWidthConfig(headerWidthConfig);

        return request;
    }

    private TableExportRequest.HeaderCell createHeaderCell(String content, Integer width, Integer height) {
        TableExportRequest.HeaderCell cell = new TableExportRequest.HeaderCell();
        cell.setContent(content);
        cell.setWidth(width);
        cell.setHeight(height);
        return cell;
    }

    private TableExportRequest.DataCell createDataCell(String content, Integer width, Integer height) {
        TableExportRequest.DataCell cell = new TableExportRequest.DataCell();
        cell.setContent(content);
        cell.setWidth(width);
        cell.setHeight(height);
        cell.setHasMath(false);
        return cell;
    }
}
